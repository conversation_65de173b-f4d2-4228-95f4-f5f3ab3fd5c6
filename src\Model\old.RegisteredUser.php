<?php

namespace App\Model;

use App\Core\Database;
use App\Helpers\UuidHelper;
use PDO;
use Exception;

/**
 * RegisteredUser Model
 *
 * Handles operations related to registered users
 */
class RegisteredUser
{
    private $id;
    private $username;
    private $email;
    private $password_hash;
    private $is_active;
    private $is_admin; // Added is_admin property
    private $registration_ip;
    private $created_at;
    private $updated_at;
    private $last_active_at;

    /**
     * Create a new registered user
     *
     * @param string $username Username
     * @param string $email Email address
     * @param string $password_hash Hashed password
     * @param string $registration_ip Registration IP address
     * @param bool $is_active Whether the user is active
     * @param bool $is_admin Whether the user is an admin
     * @return RegisteredUser
     */
    public static function create(
        string $username,
        string $email,
        string $password_hash,
        string $registration_ip,
        bool $is_active = true,
        bool $is_admin = false // Added is_admin parameter with default
    ): RegisteredUser {
        $db = Database::getInstance();

        $id = UuidHelper::generateUuid();

        $stmt = $db->prepare("
            INSERT INTO registered_users
            (id, username, email, password_hash, is_active, is_admin, registration_ip)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $id,
            $username,
            $email,
            $password_hash,
            $is_active ? 1 : 0,
            $is_admin ? 1 : 0, // Pass is_admin value
            $registration_ip
        ]);

        return self::getById($id);
    }

    /**
     * Get a registered user by ID
     *
     * @param string $id User ID
     * @return RegisteredUser|null User object or null if not found
     */
    public static function getById(string $id): ?RegisteredUser
    {
        $db = Database::getInstance();

        $stmt = $db->prepare("SELECT * FROM registered_users WHERE id = ?");
        $stmt->execute([$id]);

        $userData = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$userData) {
            return null;
        }

        return self::createFromArray($userData);
    }

    /**
     * Get a registered user by username
     *
     * @param string $username Username
     * @return RegisteredUser|null User object or null if not found
     */
    public static function getByUsername(string $username): ?RegisteredUser
    {
        $db = Database::getInstance();

        $stmt = $db->prepare("SELECT * FROM registered_users WHERE username = ?");
        $stmt->execute([$username]);

        $userData = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$userData) {
            return null;
        }

        return self::createFromArray($userData);
    }

    /**
     * Get a registered user by email
     *
     * @param string $email Email address
     * @return RegisteredUser|null User object or null if not found
     */
    public static function getByEmail(string $email): ?RegisteredUser
    {
        $db = Database::getInstance();

        $stmt = $db->prepare("SELECT * FROM registered_users WHERE email = ?");
        $stmt->execute([$email]);

        $userData = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$userData) {
            return null;
        }

        return self::createFromArray($userData);
    }

    /**
     * Get a registered user by username or email
     *
     * @param string $usernameOrEmail Username or email address
     * @return RegisteredUser|null User object or null if not found
     */
    public static function getByUsernameOrEmail(string $usernameOrEmail): ?RegisteredUser
    {
        $db = Database::getInstance();

        $stmt = $db->prepare("SELECT * FROM registered_users WHERE username = ? OR email = ?");
        $stmt->execute([$usernameOrEmail, $usernameOrEmail]);

        $userData = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$userData) {
            return null;
        }

        return self::createFromArray($userData);
    }

    /**
     * Check if a username already exists
     *
     * @param string $username Username to check
     * @return bool True if the username exists
     */
    public static function usernameExists(string $username): bool
    {
        $db = Database::getInstance();

        $stmt = $db->prepare("SELECT COUNT(*) FROM registered_users WHERE username = ?");
        $stmt->execute([$username]);

        return (int)$stmt->fetchColumn() > 0;
    }

    /**
     * Check if an email already exists
     *
     * @param string $email Email to check
     * @return bool True if the email exists
     */
    public static function emailExists(string $email): bool
    {
        $db = Database::getInstance();

        $stmt = $db->prepare("SELECT COUNT(*) FROM registered_users WHERE email = ?");
        $stmt->execute([$email]);

        return (int)$stmt->fetchColumn() > 0;
    }

    /**
     * Update the last_active_at timestamp for a user
     *
     * @param string $id User ID
     * @return bool True if successful
     */
    public static function updateLastActiveAt(string $id): bool
    {
        $db = Database::getInstance();

        $stmt = $db->prepare("UPDATE registered_users SET last_active_at = NOW() WHERE id = ?");
        return $stmt->execute([$id]);
    }

    /**
     * Create a RegisteredUser object from an array of user data
     *
     * @param array $userData User data array
     * @return RegisteredUser
     */
    private static function createFromArray(array $userData): RegisteredUser
    {
        $user = new self();

        $user->id = $userData['id'];
        $user->username = $userData['username'];
        $user->email = $userData['email'];
        $user->password_hash = $userData['password_hash'];
        $user->is_active = (bool)$userData['is_active'];
        $user->is_admin = isset($userData['is_admin']) ? (bool)$userData['is_admin'] : false; // Handle is_admin
        $user->registration_ip = $userData['registration_ip'];
        $user->created_at = $userData['created_at'];
        $user->updated_at = $userData['updated_at'];
        $user->last_active_at = $userData['last_active_at'];

        return $user;
    }

    // Getters
    public function getId(): string
    {
        return $this->id;
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getPasswordHash(): string
    {
        return $this->password_hash;
    }

    public function isActive(): bool
    {
        return $this->is_active;
    }

    public function isAdmin(): bool // Added isAdmin getter
    {
        return $this->is_admin;
    }

    public function getRegistrationIp(): string
    {
        return $this->registration_ip;
    }

    public function getCreatedAt(): string
    {
        return $this->created_at;
    }

    public function getUpdatedAt(): string
    {
        return $this->updated_at;
    }

    public function getLastActiveAt(): string
    {
        return $this->last_active_at;
    }
}
