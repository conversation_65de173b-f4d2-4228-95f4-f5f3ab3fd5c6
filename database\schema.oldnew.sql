-- Full Schema for the Cryptag application and potential future extensions
-- Timestamp: 2025-05-26
-- ON DELETE actions removed from Foreign Keys; default behavior is RESTRICT.

-- Core Tables --

CREATE TABLE IF NOT EXISTS `wallets` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `type` VARCHAR(10) NOT NULL COMMENT 'btc, eth, sol',
  `address` VARCHAR(255) NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `idx_type_address` (`type`, `address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `registered_users` (
  `id` CHAR(36) PRIMARY KEY COMMENT 'UUID from <PERSON><PERSON>',
  `username` VARCHAR(50) NOT NULL UNIQUE,
  `email` VARCHAR(255) NOT NULL UNIQUE,
  `password_hash` VARCHAR(255) NOT NULL,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'TRUE banned, FALSE otherwise',
  `is_admin` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'TRUE if admin, FALSE otherwise',
  `registration_ip` VARCHAR(45) NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_active_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Last known activity'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `anonymous_users` (
  `id` CHAR(36) PRIMARY KEY COMMENT 'UUID from PHP',
  `generated_username` VARCHAR(50) NOT NULL COMMENT 'Format: anon- followed by 10 random digits',
  `first_seen_ip` VARCHAR(45) NULL,
  `claimed_by_registered_user_id` CHAR(36) NULL,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'TRUE if user is active/not banned, FALSE otherwise',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `last_active_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp of the last known user activity, updated by application logic',
  FOREIGN KEY (`claimed_by_registered_user_id`) REFERENCES `registered_users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tagging System for Wallets --

CREATE TABLE IF NOT EXISTS `tags` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(50) NOT NULL UNIQUE COMMENT 'The tag text, e.g., #defi, #scam_alert, #tutorial',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `wallet_tags` (
  `tag_id` INT UNSIGNED NOT NULL,
  `wallet_id` INT NOT NULL COMMENT 'FK to wallets.id - the wallet being tagged',
  `tagged_by_user_id` CHAR(36) NOT NULL COMMENT 'UUID of the user who applied the tag (registered or anonymous)',
  `tagger_is_anonymous` BOOLEAN NOT NULL COMMENT 'If tagged_by_user_id is set, this indicates their type',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`tag_id`, `wallet_id`),
  FOREIGN KEY (`tag_id`) REFERENCES `tags`(`id`),
  FOREIGN KEY (`wallet_id`) REFERENCES `wallets`(`id`),
  INDEX `idx_wallet_tags_wallet` (`wallet_id`),
  INDEX `idx_wallet_tags_tagger` (`tagged_by_user_id`, `tagger_is_anonymous`)
  -- Note: No direct FK for tagged_by_user_id. Application ensures integrity if used.
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Profile Extensions --

CREATE TABLE IF NOT EXISTS `user_profiles` (
  `user_id` CHAR(36) PRIMARY KEY COMMENT 'FK to registered_users.id',
  `bio` TEXT NULL,
  `avatar_url` VARCHAR(255) NULL,
  `website_url` VARCHAR(255) NULL,
  `location` VARCHAR(100) NULL,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `registered_users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Content and Interaction Tables --

CREATE TABLE IF NOT EXISTS `messages` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `wallet_id` INT NOT NULL COMMENT 'FK to wallets.id - the target wallet/profile for the message',
  `content` TEXT NOT NULL,
  `sender_user_id` CHAR(36) NOT NULL COMMENT 'UUID of the sender, from anonymous_users or registered_users table',
  `is_anonymous_sender` BOOLEAN NOT NULL COMMENT 'TRUE if sender_user_id refers to anonymous_users, FALSE if registered_users',
  `sender_ip` VARCHAR(45) NULL COMMENT 'IP Address of the sender',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`wallet_id`) REFERENCES `wallets`(`id`),
  INDEX `idx_messages_sender` (`sender_user_id`, `is_anonymous_sender`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `message_likes` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `message_id` INT NOT NULL COMMENT 'FK to messages.id - the message being liked',
  `liker_id` CHAR(36) NOT NULL COMMENT 'UUID of the liker; references registered_users.id or anonymous_users.id',
  `is_anonymous_liker` BOOLEAN NOT NULL COMMENT 'TRUE if liker_id refers to anonymous_users.id, FALSE if registered_users.id',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`message_id`) REFERENCES `messages`(`id`),
  UNIQUE KEY `unique_liker_message_like` (`liker_id`, `is_anonymous_liker`, `message_id`),
  INDEX `idx_message_likes_message_id` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `wallet_likes` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `wallet_id` INT NOT NULL COMMENT 'FK to wallets.id - the wallet being liked',
  `liker_id` CHAR(36) NOT NULL COMMENT 'UUID of the liker; references registered_users.id or anonymous_users.id',
  `is_anonymous_liker` BOOLEAN NOT NULL COMMENT 'TRUE if liker_id refers to anonymous_users.id, FALSE if registered_users.id',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`wallet_id`) REFERENCES `wallets`(`id`),
  UNIQUE KEY `unique_liker_wallet_like` (`liker_id`, `is_anonymous_liker`, `wallet_id`),
  INDEX `idx_wallet_likes_on_wallet` (`wallet_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `message_comments` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `message_id` INT NOT NULL COMMENT 'FK to messages.id - the message being commented on',
  `parent_comment_id` BIGINT UNSIGNED NULL COMMENT 'FK to message_comments.id - for threaded replies',
  `commenter_id` CHAR(36) NOT NULL COMMENT 'UUID of the commenter (registered or anonymous)',
  `is_anonymous_commenter` BOOLEAN NOT NULL COMMENT 'TRUE if commenter_id refers to anonymous_users, FALSE if registered_users',
  `content` TEXT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`message_id`) REFERENCES `messages`(`id`),
  FOREIGN KEY (`parent_comment_id`) REFERENCES `message_comments`(`id`),
  INDEX `idx_comment_message` (`message_id`, `created_at`),
  INDEX `idx_comment_commenter` (`commenter_id`, `is_anonymous_commenter`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Social Graph / Subscription / User Control Tables --

CREATE TABLE IF NOT EXISTS `user_follows` (
  `follower_user_id` CHAR(36) NOT NULL COMMENT 'FK to registered_users.id - the user who is following',
  `followed_user_id` CHAR(36) NOT NULL COMMENT 'FK to registered_users.id - the user being followed',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`follower_user_id`, `followed_user_id`),
  FOREIGN KEY (`follower_user_id`) REFERENCES `registered_users`(`id`),
  FOREIGN KEY (`followed_user_id`) REFERENCES `registered_users`(`id`),
  INDEX `idx_followed_user_followers` (`followed_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `wallet_subscriptions` (
  `user_id` CHAR(36) NOT NULL COMMENT 'UUID of the subscriber (registered or anonymous)',
  `is_anonymous_subscriber` BOOLEAN NOT NULL COMMENT 'TRUE if user_id refers to anonymous_users.id, FALSE if registered_users.id',
  `wallet_id` INT NOT NULL COMMENT 'FK to wallets.id - the wallet being subscribed to',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`, `is_anonymous_subscriber`, `wallet_id`),
  FOREIGN KEY (`wallet_id`) REFERENCES `wallets`(`id`),
  INDEX `idx_wallet_subscriptions_wallet` (`wallet_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `user_blocks` (
  `blocker_user_id` CHAR(36) NOT NULL COMMENT 'FK to registered_users.id - the user initiating the block',
  `blocked_user_id` CHAR(36) NOT NULL COMMENT 'FK to registered_users.id - the user being blocked',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`blocker_user_id`, `blocked_user_id`),
  FOREIGN KEY (`blocker_user_id`) REFERENCES `registered_users`(`id`),
  FOREIGN KEY (`blocked_user_id`) REFERENCES `registered_users`(`id`),
  INDEX `idx_user_blocks_blocked` (`blocked_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `saved_items` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `user_id` CHAR(36) NOT NULL COMMENT 'FK to registered_users.id - the user saving the item',
  `item_id` VARCHAR(255) NOT NULL COMMENT 'ID of the saved item (e.g., message_id as string, wallet_id as string)',
  `item_type` ENUM('message', 'wallet') NOT NULL COMMENT 'Type of the item being saved',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `registered_users`(`id`),
  UNIQUE KEY `unique_user_saved_item` (`user_id`, `item_id`, `item_type`),
  INDEX `idx_saved_items_item` (`item_id`, `item_type`)
  -- Note: item_id is VARCHAR for polymorphism; application handles joins based on item_type and casting item_id.
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Private Messaging --
CREATE TABLE IF NOT EXISTS `private_messages` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `sender_id` CHAR(36) NOT NULL COMMENT 'FK to registered_users.id - the sender',
  `recipient_id` CHAR(36) NOT NULL COMMENT 'FK to registered_users.id - the recipient',
  `content` TEXT NOT NULL,
  `is_read` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'TRUE if the recipient has read the message',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `sender_deleted_at` TIMESTAMP NULL COMMENT 'Timestamp if sender deleted their copy',
  `recipient_deleted_at` TIMESTAMP NULL COMMENT 'Timestamp if recipient deleted their copy',
  FOREIGN KEY (`sender_id`) REFERENCES `registered_users`(`id`),
  FOREIGN KEY (`recipient_id`) REFERENCES `registered_users`(`id`),
  INDEX `idx_pm_conversation` (`sender_id`, `recipient_id`, `created_at`), -- For viewing a conversation thread
  INDEX `idx_pm_inbox` (`recipient_id`, `sender_id`, `created_at`), -- For recipient's inbox view
  INDEX `idx_pm_recipient_unread` (`recipient_id`, `is_read`, `created_at`) -- For unread counts/notifications
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Notifications & Activity --

CREATE TABLE IF NOT EXISTS `notifications` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `recipient_user_id` CHAR(36) NOT NULL COMMENT 'User to receive the notification (must be registered_users.id)',
  `actor_user_id` CHAR(36) NULL COMMENT 'User who performed the action (registered or anonymous), NULL if system',
  `actor_is_anonymous` BOOLEAN NULL COMMENT 'If actor_user_id is set, this indicates their type',
  `action_type` VARCHAR(50) NOT NULL COMMENT 'e.g., like_message, comment_message, user_follow, pm_received',
  `target_entity_id` VARCHAR(255) NULL COMMENT 'ID of the item related to notification (e.g., message_id, wallet_id, user_id, private_message_id)',
  `target_entity_type` VARCHAR(50) NULL COMMENT 'Type of the target entity (e.g., message, wallet, user, private_message)',
  `is_read` BOOLEAN NOT NULL DEFAULT FALSE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`recipient_user_id`) REFERENCES `registered_users`(`id`),
  INDEX `idx_notification_recipient_status` (`recipient_user_id`, `is_read`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `user_activity_log` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `user_id` CHAR(36) NOT NULL COMMENT 'UUID of the user (registered or anonymous)',
  `is_anonymous_user` BOOLEAN NOT NULL,
  `action_type` VARCHAR(100) NOT NULL COMMENT 'e.g., login_success, login_fail, password_change, message_created, wallet_liked',
  `action_details` JSON NULL COMMENT 'Optional JSON object for extra details about the action',
  `ip_address` VARCHAR(45) NULL,
  `user_agent` TEXT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_user_activity_user_time` (`user_id`, `is_anonymous_user`, `created_at`),
  INDEX `idx_user_activity_action_type_time` (`action_type`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Moderation Tables --

CREATE TABLE IF NOT EXISTS `reports` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `reported_entity_id` VARCHAR(255) NOT NULL COMMENT 'ID of the item being reported (e.g. message_id, wallet_id, user_id, comment_id)',
  `reported_entity_type` ENUM('message', 'wallet', 'user_profile', 'comment', 'private_message') NOT NULL,
  `reporter_user_id` CHAR(36) NOT NULL COMMENT 'User who submitted the report (registered or anonymous)',
  `reporter_is_anonymous` BOOLEAN NOT NULL,
  `reason` TEXT NULL,
  `status` ENUM('pending_review', 'under_review', 'action_taken', 'dismissed') NOT NULL DEFAULT 'pending_review',
  `admin_reviewer_id` CHAR(36) NULL COMMENT 'FK to registered_users.id (admin who reviewed, must have is_admin=TRUE)',
  `review_notes` TEXT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `reviewed_at` TIMESTAMP NULL,
  FOREIGN KEY (`admin_reviewer_id`) REFERENCES `registered_users`(`id`),
  INDEX `idx_report_status_created` (`status`, `created_at`),
  INDEX `idx_report_reported_item` (`reported_entity_type`, `reported_entity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Utility Tables --

CREATE TABLE IF NOT EXISTS `rate_limit_attempts` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `identifier` VARCHAR(255) NOT NULL COMMENT 'Stores IP address or UserID (CHAR(36) UUID)',
  `identifier_type` ENUM('ip_captcha_validation', 'user_message_post', 'ip_registration_attempt', 'user_login_attempt', 'private_message_send', 'like_attempt', 'comment_attempt') NOT NULL,
  `attempt_timestamp` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_rate_limit_check` (`identifier`, `identifier_type`, `attempt_timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;