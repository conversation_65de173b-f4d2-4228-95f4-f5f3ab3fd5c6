/* FAQ Page Styles */
.faq-container {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-md);
}

/* .faq-title removed - now using .page-header from style.css */

.faq-description {
    color: var(--color-text-secondary);
    text-align: center;
    margin-bottom: var(--spacing-xl);
    font-size: 1.1rem;
}

.faq-accordion {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.faq-item {
    /* Inherits from .card */
    padding: 0; /* Override default card padding to allow accordion content to control it */
    overflow: hidden; /* Keep specific overflow */
    transition: all var(--transition-normal); /* Keep specific transition */
}

.faq-item:hover {
    box-shadow: var(--shadow-md); /* Keep specific shadow on hover */
}

.faq-question {
    width: 100%;
    background-color: var(--color-surface);
    color: var(--color-text-primary);
    padding: var(--spacing-md) var(--spacing-lg); /* This padding is correct for the button */
    border: none;
    text-align: left;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all var(--transition-fast);
}

.faq-question:hover {
    background-color: var(--color-surface-accent);
}

.faq-icon {
    transition: transform var(--transition-fast);
    color: var(--color-text-secondary);
    width: 1.2rem;
    height: 1.2rem;
}

.faq-item.active .faq-icon {
    transform: rotate(180deg);
    color: var(--color-primary);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-normal), padding var(--transition-normal);
    padding: 0 var(--spacing-lg); /* This padding is correct for the closed answer */
}

.faq-item.active .faq-answer {
    max-height: 500px; /* Adjust as needed */
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg); /* This padding is correct for the open answer */
}

.faq-answer p {
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin: 0;
}

.faq-answer a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.faq-answer a:hover {
    color: var(--color-primary-hover);
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    /* .faq-title responsive rule removed - now handled by .page-header in style.css */
    
    .faq-description {
        font-size: 1rem;
    }
    
    .faq-question {
        font-size: 1rem;
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .faq-answer {
        padding: 0 var(--spacing-md);
    }
    
    .faq-item.active .faq-answer {
        padding: var(--spacing-sm) var(--spacing-md) var(--spacing-md);
    }
}
