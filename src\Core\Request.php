<?php

namespace App\Core;

/**
 * Encapsulates HTTP request data.
 * Provides methods to access sanitized request information.
 */
class Request
{
    private array $getParams;
    private array $postParams;
    private array $serverParams;
    private array $files;
    private string $uri;
    private string $method;

    public function __construct()
    {
        // Basic sanitization - consider more robust filtering/validation as needed
        $this->getParams = $this->sanitize($_GET);
        $this->postParams = $this->sanitize($_POST);
        $this->serverParams = $_SERVER; // Generally less risky, but be cautious accessing directly
        $this->files = $_FILES; // File uploads need specific handling/validation

        $this->uri = $this->parseUri();
        $this->method = strtoupper($_SERVER['REQUEST_METHOD'] ?? 'GET');
    }

    /**
     * Basic sanitization for GET/POST data.
     * Uses filter_input_array for basic safety.
     *
     * @param array $data The raw input array (e.g., $_GET, $_POST)
     * @return array The sanitized array
     */
    private function sanitize(array $data): array
    {
        // Using FILTER_DEFAULT might allow potentially harmful tags/chars.
        // Consider FILTER_SANITIZE_STRING or more specific filters per input field later.
        return filter_var_array($data, FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?: [];
    }

    /**
     * Parses the request URI. Prioritizes 'url' query param from .htaccess rewrite.
     * Falls back to calculating relative path from REQUEST_URI and SCRIPT_NAME.
     * Ensures the URI is relative to the application's entry point (index.php).
     *
     * @return string The parsed URI path relative to the application root (e.g., '/search', '/address/btc/123').
     */
    private function parseUri(): string
    {
        // Prioritize the 'url' parameter from .htaccess rewrite if available
        // This is often more reliable in subdirectory setups.
        if (isset($_GET['url'])) {
            $uriPath = trim(filter_input(INPUT_GET, 'url', FILTER_SANITIZE_URL) ?: '');
            // Ensure it starts with a slash
            if (!str_starts_with($uriPath, '/')) {
                $uriPath = '/' . $uriPath;
            }
            // Remove trailing slash unless it's the root path '/'
            return rtrim($uriPath, '/') ?: '/';
        }

        // Fallback to parsing REQUEST_URI if 'url' param is not set
        $rawUri = $_SERVER['REQUEST_URI'] ?? '/';
        $uriPath = parse_url($rawUri, PHP_URL_PATH) ?: '/'; // Ensure we have a string

        // Determine the base path dynamically from the script name
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? ''; // e.g., /CRYPTAG/public/index.php or /index.php
        $scriptDir = dirname($scriptName); // e.g., /CRYPTAG/public or /

        // If the script is directly in the web root, the base path is effectively '/'
        if ($scriptDir === '/' || $scriptDir === '\\') {
             $basePath = '/';
        } else {
            // Otherwise, the base path is the directory containing the script's directory
            $basePath = dirname($scriptDir); // e.g., /CRYPTAG or /
             // Normalize basePath to ensure it ends with a slash if it's not the root
            if ($basePath !== '/' && $basePath !== '\\') {
                // Ensure consistent directory separators for comparison
                $basePath = rtrim(str_replace('\\', '/', $basePath), '/') . '/';
            } else {
                $basePath = '/'; // Ensure root is just '/'
            }
        }

        // Ensure uriPath uses forward slashes for comparison
        $uriPath = str_replace('\\', '/', $uriPath);

        // Remove the base path from the beginning of the URI path, if present
        if ($basePath !== '/' && str_starts_with($uriPath, $basePath)) {
            $uriPath = substr($uriPath, strlen($basePath));
        }

        // Ensure the path starts with a slash after stripping the base path
        if (!str_starts_with($uriPath, '/')) {
            $uriPath = '/' . $uriPath;
        }

        // Remove trailing slash unless it's the root path '/'
        return rtrim($uriPath, '/') ?: '/';
    }


    /**
     * Get the request URI path relative to the application root.
     *
     * @return string
     */
    public function getUri(): string
    {
        return $this->uri;
    }

    /**
     * Get the request method (GET, POST, etc.).
     *
     * @return string
     */
    public function getMethod(): string
    {
        return $this->method;
    }

    /**
     * Get a specific GET parameter.
     *
     * @param string $key The parameter key
     * @param mixed $default Default value if key not found
     * @return mixed
     */
    public function get(string $key, mixed $default = null): mixed
    {
        return $this->getParams[$key] ?? $default;
    }

    /**
     * Get all GET parameters.
     *
     * @return array
     */
    public function allGet(): array
    {
        return $this->getParams;
    }

    /**
     * Get a specific POST parameter.
     *
     * @param string $key The parameter key
     * @param mixed $default Default value if key not found
     * @return mixed
     */
    public function post(string $key, mixed $default = null): mixed
    {
        return $this->postParams[$key] ?? $default;
    }

    /**
     * Get all POST parameters.
     *
     * @return array
     */
    public function allPost(): array
    {
        return $this->postParams;
    }

    /**
     * Get a specific SERVER parameter. Use with caution.
     *
     * @param string $key The parameter key
     * @param mixed $default Default value if key not found
     * @return mixed
     */
    public function server(string $key, mixed $default = null): mixed
    {
        return $this->serverParams[$key] ?? $default;
    }

    /**
     * Get uploaded file information. Requires specific validation/handling.
     *
     * @param string $key The file input name
     * @return array|null File info array or null if not found
     */
    public function file(string $key): ?array
    {
        return $this->files[$key] ?? null;
    }
}