/**
 * CAPTCHA Management Module
 * Handles Cloudflare Turnstile CAPTCHA functionality
 */

const CaptchaManager = {
    // Configuration
    config: {
        siteKey: '0x4AAAAAABd0bx4rcY4I6QrG',
        theme: 'dark',
        size: 'normal'
    },

    // State
    isInitialized: false,
    widgetId: null,

    /**
     * Initialize the CAPTCHA manager
     * @param {HTMLElement} form - The form element to attach CAPTCHA to
     */
    init: function(form) {
        this.form = form;
    },

    /**
     * Show CAPTCHA requirement to user
     * @param {boolean} showToast - Whether to show a warning toast (default: true)
     */
    show: function(showToast = true) {
        // Find the existing CAPTCHA section in the HTML
        const captchaSection = document.querySelector('.captcha-section');

        if (captchaSection) {
            // Show the CAPTCHA section
            captchaSection.style.display = 'block';

            // Initialize Turnstile if not already done
            if (!this.isInitialized) {
                this.initializeTurnstile();
            }
        }

        if (showToast && window.toastManager) {
            window.toastManager.warning('Please complete the CAPTCHA to post your message');
        }
    },

    /**
     * Initialize Cloudflare Turnstile
     */
    initializeTurnstile: function() {
        // Load Turnstile script if not already loaded
        if (!window.turnstile) {
            const script = document.createElement('script');
            script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js?onload=onloadTurnstileCallback';
            script.async = true;
            script.defer = true;
            document.head.appendChild(script);

            // Set up callback
            window.onloadTurnstileCallback = () => {
                this.renderTurnstile();
            };
        } else {
            this.renderTurnstile();
        }
    },

    /**
     * Render the Turnstile widget
     */
    renderTurnstile: function() {
        if (window.turnstile && document.getElementById('cf-turnstile-container')) {
            try {
                this.widgetId = window.turnstile.render('#cf-turnstile-container', {
                    sitekey: this.config.siteKey,
                    theme: this.config.theme,
                    callback: (token) => {
                        this.onSuccess(token);
                    },
                    'error-callback': (error) => {
                        this.onError(error);
                    },
                    'expired-callback': () => {
                        this.onExpired();
                    }
                });
                this.isInitialized = true;
            } catch (error) {
                console.error('Error rendering Turnstile:', error);
            }
        }
    },

    /**
     * Get the current CAPTCHA response token
     * @returns {string|null} The CAPTCHA response token or null if not available
     */
    getResponse: function() {
        const turnstileInput = document.getElementById('cf-turnstile-response');
        if (turnstileInput && turnstileInput.value) {
            return turnstileInput.value;
        }
        return null;
    },

    /**
     * Reset the CAPTCHA widget
     */
    reset: function() {
        if (typeof turnstile !== 'undefined' && this.widgetId !== null) {
            try {
                turnstile.reset(this.widgetId);
            } catch (error) {
                console.error('Error resetting Turnstile:', error);
            }
        }

        // Clear the hidden input
        const turnstileInput = document.getElementById('cf-turnstile-response');
        if (turnstileInput) {
            turnstileInput.value = '';
        }
    },

    /**
     * Hide the CAPTCHA section
     */
    hide: function() {
        const captchaSection = document.querySelector('.captcha-section');
        if (captchaSection) {
            captchaSection.style.display = 'none';
        }
    },

    /**
     * Remove the CAPTCHA from the DOM (not needed with static HTML)
     */
    remove: function() {
        this.hide();
        this.isInitialized = false;
        this.widgetId = null;
    },

    /**
     * CAPTCHA success callback
     * @param {string} token - The CAPTCHA response token
     */
    onSuccess: function(token) {
        const turnstileInput = document.getElementById('cf-turnstile-response');
        if (turnstileInput) {
            turnstileInput.value = token;
        }
    },

    /**
     * CAPTCHA error callback
     * @param {string} error - The error message
     */
    onError: function(error) {
        if (window.toastManager) {
            window.toastManager.error('CAPTCHA error. Please try again.');
        }
    },

    /**
     * CAPTCHA expired callback
     */
    onExpired: function() {
        const turnstileInput = document.getElementById('cf-turnstile-response');
        if (turnstileInput) {
            turnstileInput.value = '';
        }
        if (window.toastManager) {
            window.toastManager.warning('CAPTCHA expired. Please complete it again.');
        }
    },

    /**
     * Check if CAPTCHA is currently visible
     * @returns {boolean} True if CAPTCHA is visible
     */
    isVisible: function() {
        const captchaSection = document.querySelector('.captcha-section');
        return captchaSection && captchaSection.style.display !== 'none';
    },

    /**
     * Check if CAPTCHA is completed
     * @returns {boolean} True if CAPTCHA is completed
     */
    isCompleted: function() {
        const response = this.getResponse();
        return response !== null && response.length > 0;
    }
};

// Export for use in other modules
window.CaptchaManager = CaptchaManager;
