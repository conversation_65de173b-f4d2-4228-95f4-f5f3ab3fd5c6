<?php

// Define a base path constant for easier file includes
define('BASE_PATH', dirname(__DIR__)); // Points to the 'cryptag' root directory

// Register the Autoloader
require_once BASE_PATH . '/src/Core/Autoloader.php';
App\Core\Autoloader::register();

// Initialize JWT Helper (to load secret key)
App\Helpers\JwtHelper::init();

// --- Framework Initialization ---

// Load Configuration
$config = require BASE_PATH . '/config/config.php';

// Load Database Configuration (if db config exists)
if (isset($config['database'])) {
    App\Core\Database::loadConfig($config['database']);
}

// Determine current user (for view rendering)
$currentUser = App\Helpers\JwtHelper::getCurrentUser();

// Set Error Reporting based on environment
if (isset($config['app']['env']) && $config['app']['env'] === 'development' && $config['app']['debug'] === true) {
    ini_set('display_errors', 1); // Display errors in browser (useful for HTML views)
    ini_set('log_errors', 1);     // Ensure errors are logged to file
    // Optionally set error_log path if needed: ini_set('error_log', BASE_PATH . '/logs/php_error.log');
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    ini_set('log_errors', 1); // Log errors in production too
    // Optionally set error_log path for production
    error_reporting(E_ALL & ~E_DEPRECATED & ~E_STRICT); // Log most things but maybe not notices/deprecations
}

// Create Request Object
$request = new App\Core\Request();

// Create Router Object
$router = new App\Core\Router();

// --- Define Routes ---
$router->get('/', [App\Controller\HomeController::class, 'index']);
$router->get('/faq', [App\Controller\FaqController::class, 'index']);
$router->get('/about', [App\Controller\AboutController::class, 'index']);
$router->get('/roadmap', [App\Controller\RoadmapController::class, 'index']);
$router->get('/login', [App\Controller\AuthViewController::class, 'login']);
$router->get('/register', [App\Controller\AuthViewController::class, 'register']);
$router->post('/search', [App\Controller\SearchController::class, 'handleSearch']);

// Address Profile Routes
$router->get('/address/{type}/{address}', [App\Controller\AddressController::class, 'showProfile']); // HTML view

// API Routes for Address Profile Messages (AJAX)
$router->get('/api/address/{type}/{address}/messages', [App\Controller\AddressController::class, 'getMessagesJson']); // Fetch messages (JSON)
$router->post('/api/address/{type}/{address}/add-message', [App\Controller\MessageController::class, 'addMessage']); // Updated to use MessageController

// New API Routes for User Authentication
$router->post('/api/messages', [App\Controller\MessageController::class, 'addMessage']);
$router->post('/api/auth/register', [App\Controller\AuthController::class, 'register']);
$router->post('/api/auth/login', [App\Controller\AuthController::class, 'login']);
$router->post('/api/auth/logout', [App\Controller\AuthController::class, 'logout']);

// Tag API Routes
$router->get('/api/tags/wallet-data/{walletId}', [App\Controller\TagController::class, 'getWalletTagData']);
$router->post('/api/tags/add', [App\Controller\TagController::class, 'addTag']);

$router->get('/@{walletAddress}', [\App\Controller\SearchController::class, 'handleVanityUrl']); // New route for /@walletAddress

// --- Dispatch Request ---
try {
    $routeInfo = $router->dispatch($request);

    $controllerName = $routeInfo['action'][0];
    $methodName = $routeInfo['action'][1];
    $params = $routeInfo['params']; // Route parameters like {type}, {address}

    // Basic Controller Instantiation and Method Call
    if (class_exists($controllerName)) {
        $controller = new $controllerName(); // Consider Dependency Injection later

        // Pass the current user to the controller if it has a method to receive it
        if (method_exists($controller, 'setCurrentUser')) {
            $controller->setCurrentUser($currentUser);
        }

        if (method_exists($controller, $methodName)) {
            // Call the controller method, passing route parameters
            // The controller method will need to instantiate Request itself if needed
            call_user_func_array([$controller, $methodName], $params);
        } else {
            // Use Throwable for better compatibility if needed, Exception is fine for now
            throw new \Exception("Method {$methodName} not found in controller {$controllerName}", 500);
        }
    } else {
        throw new \Exception("Controller {$controllerName} not found", 500);
    }

} catch (\Throwable $e) { // Catch Throwable for broader error catching (PHP 7+)
    $errorCode = $e->getCode() ?: 500;
    // Ensure error code is a valid integer HTTP status code
    if (!is_int($errorCode) || $errorCode < 400 || $errorCode > 599) {
        $errorCode = 500;
    }

    // Log the error *before* setting response code or outputting anything
    error_log("Uncaught Exception: " . $e->getMessage() . "\nTrace:\n" . $e->getTraceAsString());

    // Set HTTP response code *after* logging
    http_response_code($errorCode);

    // Check if the client expects JSON
    $acceptHeader = $_SERVER['HTTP_ACCEPT'] ?? '';
    $prefersJson = str_contains(strtolower($acceptHeader), 'application/json');

    if ($prefersJson) {
        // Output JSON error for API requests
        header('Content-Type: application/json');
        $errorResponse = ['success' => false, 'error' => 'An internal server error occurred.'];
        // Add more detail in development mode
        if (isset($config['app']['env']) && $config['app']['env'] === 'development' && $config['app']['debug'] === true) {
            $errorResponse['error_message'] = $e->getMessage();
            $errorResponse['error_trace'] = explode("\n", $e->getTraceAsString());
        }
        echo json_encode($errorResponse);

    } else {
        // Redirect to homepage for HTML requests - better UX than showing error page
        $baseUrl = $config['app']['base_url'] ?? '/';
        header("Location: {$baseUrl}");
        exit;
    }
    exit; // Ensure script stops after handling the error
}

?>
