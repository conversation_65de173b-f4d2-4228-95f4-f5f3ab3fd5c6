// Login page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    const loginButton = document.getElementById('login-button');
    const originalButtonText = loginButton.textContent.trim();

    // Get base URL from global variable (set by PHP)
    const baseUrl = window.baseUrl || '/cryptag/';

    // Initialize CAPTCHA manager
    if (window.CaptchaManager) {
        CaptchaManager.init(loginForm);
        CaptchaManager.show(false); // Show CAPTCHA without toast
    }

    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const emailOrUsername = document.getElementById('email_or_username').value;
        const password = document.getElementById('password').value;
        const turnstileResponse = CaptchaManager ? CaptchaManager.getResponse() : document.getElementById('cf-turnstile-response').value;

        // Get anonymous user linking data
        const linkAnonymousCheckbox = document.getElementById('link_anonymous_account');
        const shouldLinkAnonymous = linkAnonymousCheckbox && linkAnonymousCheckbox.checked;

        // Validate form
        if (!emailOrUsername || !password) {
            toastManager.error('Email/username and password are required');
            return;
        }

        if (!turnstileResponse) {
            toastManager.error('Please complete the CAPTCHA');
            return;
        }

        // Show loading state
        loginButton.disabled = true;
        loginButton.innerHTML = '<span class="loading"></span> Logging in...';

        // Send login request
        fetch(baseUrl + 'api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email_or_username: emailOrUsername,
                password: password,
                cf_turnstile_response: turnstileResponse,
                link_anonymous_account: shouldLinkAnonymous
            }),
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success toast
                toastManager.success('Login successful! Redirecting...');

                // DEBUG: Check if cookies were set after login
                console.log('=== LOGIN SUCCESS DEBUG ===');
                console.log('Response data:', data);
                console.log('Cookies after login:', document.cookie);
                console.log('Auth cookie present:', document.cookie.includes('auth_token='));
                console.log('CSRF cookie present:', document.cookie.includes('csrf_token='));

                // Redirect to home page on success after a short delay
                setTimeout(() => {
                    console.log('Redirecting... Final cookie check:', document.cookie);
                    window.location.href = baseUrl;
                }, 1000);
            } else {
                // Show error toast
                toastManager.error(data.error || 'Login failed');

                // Reset button
                loginButton.disabled = false;
                loginButton.innerHTML = originalButtonText;

                // Reset CAPTCHA
                if (CaptchaManager) {
                    CaptchaManager.reset();
                }
            }
        })
        .catch(error => {
            console.error('Login error:', error);
            toastManager.error('An error occurred. Please try again.');

            // Reset button
            loginButton.disabled = false;
            loginButton.innerHTML = originalButtonText;

            // Reset CAPTCHA
            if (CaptchaManager) {
                CaptchaManager.reset();
            }
        });
    });
});
