<?php
// Basic security: Ensure this script is not accessed directly
defined('BASE_PATH') or exit('No direct script access allowed');

// Page specific variables
$pageTitle = "Cryptag Product Roadmap";
$pageDescription = "Our one-year public product roadmap for Cryptag, detailing upcoming features and plans from May 2025 to May 2026.";
$canonicalUrl = $this->getBaseUrl() . "roadmap"; // Assuming getBaseUrl() is available from BaseController

// Data from roadmap-2.txt, structured for easier rendering
// In a real scenario, this might come from a database or a more structured file (e.g., JSON, YAML)
$roadmapTitle = "Cryptag: One-Year Public Product Roadmap (May 2025 - May 2026)";
$vision = "To make blockchain addresses more understandable and interactive by allowing users to share context, claim ownership, and build a community around public address information.";
$currentStatus = "Cryptag is LIVE! You can currently search for Bitcoin, Ethereum, and Solana wallet addresses and view basic information.";

$quarters = [
    "Q2 2025" => [
        "title" => "Laying the Foundation for Interaction",
        "period" => "(Rest of May - June 2025)",
        "focus" => "Enabling initial user interaction and wallet page claiming.",
        "features" => [
            "<strong>User Messaging & Tagging (Phase 1 - Anonymous):</strong> Launch the ability for users to leave anonymous public messages and tags on any wallet page. Implement basic spam filtering (keyword-based, rate limiting).",
            "<strong>Wallet Connection:</strong> Integrate wallet connection functionality (e.g., MetaMask for Ethereum, Phantom for Solana, generic support for Bitcoin wallets where applicable for signing).",
            "<strong>Wallet Page Claiming (MVP):</strong> Introduce the ability for users to claim ownership of their wallet address page. Workflow: Connect wallet, sign a verification message. Initial claiming fee ($10 equivalent in crypto) processing (may involve manual verification for MVP).",
            "<strong>Basic Claimed Wallet Page:</strong> Claimed pages will display an \"Owner Claimed\" status. Owners can set a custom Wallet Name/Alias for their claimed page.",
            "<strong>UI/UX Enhancements:</strong> Improve the display of wallet pages, clearly distinguishing user messages from future owner-curated content. Launch a dedicated \"Roadmap\" page on the site."
        ]
    ],
    "Q3 2025" => [
        "title" => "Enhancing Ownership & Introducing Monetization",
        "period" => "(July - September 2025)",
        "focus" => "Expanding features for claimed wallet owners and introducing the first monetization feature beyond claims.",
        "features" => [
            "<strong>Enhanced Claimed Page Editing:</strong> Allow claimed owners to add a detailed description to their wallet page. Allow claimed owners to curate a list of \"Official Tags\" for their page.",
            "<strong>Automated Claim Payment Verification:</strong> Implement robust, automated systems for verifying the claiming fee payment.",
            "<strong>User Messaging & Tagging (Phase 2 - Connected Wallet):</strong> Allow users to post messages/tags while connected with their wallet, displaying their sending address (or ENS/equivalent) next to their contribution.",
            "<strong>Monetization: Paid \"Awards\":</strong> Introduce the ability for users to purchase and bestow cosmetic \"Awards\" or \"Badges\" on any wallet page (their own or others'). Awards will be visually distinct and displayed prominently.",
            "<strong>Improved Wallet Page Layout:</strong> Redesign wallet pages for better information hierarchy, clearly separating owner content, user messages, and awards."
        ]
    ],
    "Q4 2025" => [
        "title" => "Trust, Security & Community Building",
        "period" => "(October - December 2025)",
        "focus" => "Addressing critical challenges like spam and abuse, and fostering a trustworthy environment.",
        "features" => [
            "<strong>Advanced Spam & Abuse Mitigation (Phase 1):</strong> Implement more sophisticated spam filtering techniques (e.g., CAPTCHAs, improved rate limiting based on user behavior). Introduce a user-based flagging system for messages/tags.",
            "<strong>\"Verified Owner\" Badge:</strong> Visually distinct \"Verified Owner\" badge for claimed pages to enhance trust.",
            "<strong>Content Reporting & Basic Moderation Tools:</strong> Allow users to report offensive or spammy content. Develop internal tools for admin review and moderation of reported content.",
            "<strong>Community Guidelines:</strong> Publish clear community guidelines regarding acceptable content and platform use.",
            "<strong>Security Audit & Enhancements:</strong> Conduct an internal security review of the platform, focusing on wallet interactions and payment processing."
        ]
    ],
    "Q1 2026" => [
        "title" => "Expansion & Refinement",
        "period" => "(January - March 2026)",
        "focus" => "Expanding platform reach and refining existing features based on user feedback.",
        "features" => [
            "<strong>Support for New Blockchain(s):</strong> Based on community demand and feasibility, begin research and implementation for adding support for one new major blockchain (e.g., Polygon, BNB Chain, etc.).",
            "<strong>User Feedback Portal:</strong> Launch a dedicated portal for users to submit feature requests, bug reports, and provide feedback.",
            "<strong>Enhanced Search & Filtering:</strong> Improve search functionality with filters (e.g., filter messages by date, search within messages on a page).",
            "<strong>UI/UX Polish:</strong> Implement UI/UX improvements based on accumulated user feedback and analytics.",
            "<strong>Advanced Spam & Abuse Mitigation (Phase 2):</strong> Explore and implement further anti-spam measures, potentially including reputation scores for posters or requiring small crypto \"stakes\" for posting in certain contexts."
        ]
    ],
    "Q2 2026" => [
        "title" => "Scaling & Strategic Growth",
        "period" => "(April - May 2026)",
        "focus" => "Ensuring platform scalability, exploring integrations, and planning for the next year.",
        "features" => [
            "<strong>Scalability & Performance Review:</strong> Assess platform performance under load and implement optimizations for database and application layers.",
            "<strong>API Exploration (Private Beta):</strong> Begin development of a private API for potential partners or high-volume users to access Cryptag data (e.g., for displaying tags in their own apps).",
            "<strong>Potential Wallet/Explorer Integration Research:</strong> Investigate the feasibility and interest in integrating Cryptag's features (like displaying tags) directly within popular wallets or blockchain explorers.",
            "<strong>Strategic Review & Next Year's Roadmap Planning:</strong> Analyze user adoption, feedback, and market trends to plan the roadmap for June 2026 - June 2027."
        ]
    ]
];

$ongoingItems = [
    "<strong>Security Monitoring & Updates:</strong> Continuous monitoring for security vulnerabilities and timely application of patches and updates.",
    "<strong>User Support:</strong> Providing timely responses to user inquiries and issues.",
    "<strong>Community Engagement:</strong> Actively engaging with the user community on social media and other relevant platforms.",
    "<strong>Performance Optimization:</strong> Regularly reviewing and optimizing platform speed and efficiency.",
    "<strong>Legal & Regulatory Compliance:</strong> Staying updated on evolving regulations in the crypto space that may impact the platform."
];

$closingStatement = "This roadmap provides a directional plan. We will remain flexible and adapt based on community feedback, technological advancements, and the evolving crypto landscape. We're excited to build Cryptag with you!";

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    <meta name="description" content="<?= htmlspecialchars($pageDescription) ?>">
    <link rel="canonical" href="<?= htmlspecialchars($canonicalUrl) ?>" />
    <!-- Link to your main stylesheet -->
    <link rel="stylesheet" href="<?= htmlspecialchars($this->getBaseUrl()) ?>css/style.css">
    <!-- Link to the new roadmap specific stylesheet -->
    <link rel="stylesheet" href="<?= htmlspecialchars($this->getBaseUrl()) ?>css/roadmap.css">
    <!-- Include other common head elements like favicons, etc. -->
</head>
<body>

    <!-- Assuming your main layout includes a header, navigation, and footer -->
    <!-- This content will be injected into the $content variable of your main.php layout -->

    <div class="container roadmap-page">
        <h1><?= htmlspecialchars($roadmapTitle) ?></h1>

        <div class="roadmap-header">
            <p><strong>Our Vision:</strong> <?= htmlspecialchars($vision) ?></p>
            <p><strong>Current Status (May 2025):</strong> <?= htmlspecialchars($currentStatus) ?></p>
        </div>

        <hr>

        <?php foreach ($quarters as $qNumber => $qData): ?>
            <section class="card roadmap-quarter">
                <h2><?= htmlspecialchars($qNumber) ?>: <?= htmlspecialchars($qData['title']) ?> <em><?= htmlspecialchars($qData['period']) ?></em></h2>
                <p class="focus-text"><strong>Focus:</strong> <?= htmlspecialchars($qData['focus']) ?></p>
                <h3>Key Features & Implementations:</h3>
                <ul class="key-features">
                    <?php foreach ($qData['features'] as $feature): ?>
                        <li><?= $feature // Already contains HTML strong tags, so not double-encoding ?></li>
                    <?php endforeach; ?>
                </ul>
            </section>
            <?php if (array_key_last($quarters) !== $qNumber): ?>
                <hr>
            <?php endif; ?>
        <?php endforeach; ?>

        <hr>

        <section class="ongoing-section">
            <h2>Ongoing Throughout the Year</h2>
            <ul>
                <?php foreach ($ongoingItems as $item): ?>
                    <li><?= $item // Already contains HTML strong tags ?></li>
                <?php endforeach; ?>
            </ul>
        </section>

        <hr>

        <div class="roadmap-footer">
            <p><?= htmlspecialchars($closingStatement) ?></p>
        </div>
    </div>

    <!-- Assuming your main layout includes scripts at the bottom -->

</body>
</html>