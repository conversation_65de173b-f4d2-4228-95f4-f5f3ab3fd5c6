/**
 * Toast Notification System
 * Displays toast notifications that slide in from the bottom right,
 * show for 3 seconds, and then slide out.
 */
class ToastManager {
    constructor() {
        this.container = null;
        this.toastQueue = [];
        this.activeToasts = 0;
        this.maxActiveToasts = 5;
        this.maxQueueSize = 10;
        this.displayDuration = 3000; // 3 seconds
        this.initialize();
    }

    /**
     * Initialize the toast container
     */
    initialize() {
        // Create container if it doesn't exist
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.className = 'toast-container';
            document.body.appendChild(this.container);
        }
    }

    /**
     * Show a toast notification
     * @param {string} message - The message to display
     * @param {string} type - The type of toast (success, error, warning, info)
     */
    show(message, type = 'info') {
        // If we've reached the queue limit, remove the oldest queued toast
        if (this.toastQueue.length >= this.maxQueueSize) {
            this.toastQueue.shift();
        }

        // Create toast object
        const toastData = {
            message,
            type,
            id: Date.now() + Math.random().toString(36).substr(2, 5)
        };

        // If we can show more toasts, display it immediately
        if (this.activeToasts < this.maxActiveToasts) {
            this.displayToast(toastData);
        } else {
            // Otherwise, add to queue
            this.toastQueue.push(toastData);
        }
    }

    /**
     * Display a toast notification
     * @param {Object} toastData - The toast data object
     */
    displayToast(toastData) {
        this.activeToasts++;

        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast toast-${toastData.type}`;
        toast.id = `toast-${toastData.id}`;

        // Get appropriate icon based on type (using icons from the Lucide list)
        let icon = '';
        let iconName = '';
        switch (toastData.type) {
            case 'success':
                icon = '✓';
                iconName = 'check-circle'; // From Lucide list
                break;
            case 'error':
                icon = '✕';
                iconName = 'x-circle'; // From Lucide list
                break;
            case 'warning':
                icon = '⚠';
                iconName = 'alert-triangle'; // From Lucide list
                break;
            case 'info':
                icon = 'ℹ';
                iconName = 'info'; // From Lucide list
                break;
        }

        // Create toast content
        toast.innerHTML = `
            <div class="toast-layout">
                <span class="toast-icon">${icon}</span>
                <div class="toast-content">${toastData.message}</div>
                <button class="toast-close" aria-label="Close">&times;</button>
            </div>
            <div class="toast-progress"></div>
        `;

        // Add data-lucide attribute for icon replacement
        const iconSpan = toast.querySelector('.toast-icon');
        if (iconSpan && typeof lucide !== 'undefined') {
            // Create a Lucide icon element
            const iconElement = document.createElement('i');
            iconElement.setAttribute('data-lucide', iconName);
            // Clear the span and add the icon
            iconSpan.textContent = '';
            iconSpan.appendChild(iconElement);
            // Initialize the icon
            lucide.createIcons();
        }

        // Add to container
        this.container.appendChild(toast);

        // Add close event listener
        const closeButton = toast.querySelector('.toast-close');
        closeButton.addEventListener('click', () => {
            this.removeToast(toast, toastData.id);
        });

        // Show toast with animation
        setTimeout(() => {
            toast.classList.add('show');
            const progressBar = toast.querySelector('.toast-progress');
            progressBar.classList.add('active');
        }, 10);

        // Set timeout to remove toast
        setTimeout(() => {
            this.removeToast(toast, toastData.id);
        }, this.displayDuration);
    }

    /**
     * Remove a toast notification
     * @param {HTMLElement} toast - The toast element
     * @param {string} id - The toast ID
     */
    removeToast(toast, id) {
        // Add hide class for animation
        toast.classList.add('hide');
        toast.classList.remove('show');

        // Remove after animation completes
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
                this.activeToasts--;

                // Process next toast in queue if any
                if (this.toastQueue.length > 0 && this.activeToasts < this.maxActiveToasts) {
                    const nextToast = this.toastQueue.shift();
                    this.displayToast(nextToast);
                }
            }
        }, 300); // Match the CSS transition duration
    }

    /**
     * Show a success toast
     * @param {string} message - The message to display
     */
    success(message) {
        this.show(message, 'success');
    }

    /**
     * Show an error toast
     * @param {string} message - The message to display
     */
    error(message) {
        this.show(message, 'error');
    }

    /**
     * Show a warning toast
     * @param {string} message - The message to display
     */
    warning(message) {
        this.show(message, 'warning');
    }

    /**
     * Show an info toast
     * @param {string} message - The message to display
     */
    info(message) {
        this.show(message, 'info');
    }
}

// Create a global toast manager instance
const toastManager = new ToastManager();

// Export for use in other modules
window.toastManager = toastManager;
