/**
 * FAQ Accordion Functionality
 * Handles the expand/collapse functionality of the FAQ accordion
 */

document.addEventListener('DOMContentLoaded', () => {
    // Get all FAQ question buttons
    const faqQuestions = document.querySelectorAll('.faq-question');
    
    // Add click event listener to each question
    faqQuestions.forEach(question => {
        question.addEventListener('click', () => {
            // Get the parent FAQ item
            const faqItem = question.parentElement;
            
            // Check if this item is already active
            const isActive = faqItem.classList.contains('active');
            
            // Close all FAQ items first (optional - remove if you want multiple open at once)
            document.querySelectorAll('.faq-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Toggle the active class on the clicked item
            if (!isActive) {
                faqItem.classList.add('active');
            }
        });
    });
    
    // Optional: Open the first FAQ item by default
    // Uncomment the next line if you want the first item to be open initially
    // document.querySelector('.faq-item')?.classList.add('active');
});
