<?php
// Ensure variables are set and escape output
$code = isset($errorCode) ? (int) $errorCode : 500;
$title = isset($pageTitle) ? htmlspecialchars($pageTitle, ENT_QUOTES, 'UTF-8') : 'Error';
$message = isset($errorMessage) ? htmlspecialchars($errorMessage, ENT_QUOTES, 'UTF-8') : 'An unexpected error occurred.';

// Set page title if not already set by controller (used in main layout)
if (!isset($pageTitle)) {
    $pageTitle = "Error {$code}";
}
?>

<div class="error-container">
    <div class="logo-graphic">
        <!-- Diamond-shaped logo with CT is styled in CSS -->
    </div>
    
    <h2><?= $title ?></h2>
    
    <div class="error-message">
        <p><?= $message ?></p>
        <p>(Error Code: <?= $code ?>)</p>
    </div>
    
    <p class="return-link">
        <a href="/">Return to Homepage</a>
    </p>
</div>

<?php
// Debug info placeholder - keep commented out for production
/*
if (isset($config['app']['env']) && $config['app']['env'] === 'development' && isset($stackTrace)) {
    echo '<h3>Debug Information (Development Mode)</h3>';
    echo '<pre>' . htmlspecialchars($stackTrace, ENT_QUOTES, 'UTF-8') . '</pre>';
}
*/
?>