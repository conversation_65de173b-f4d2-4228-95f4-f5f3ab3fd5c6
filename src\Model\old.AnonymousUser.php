<?php

namespace App\Model;

use App\Core\Database;
use App\Helpers\UuidHelper;
use PDO;
use Exception;

/**
 * AnonymousUser Model
 *
 * Handles operations related to anonymous users
 */
class AnonymousUser
{
    private $id;
    private $generated_username;
    private $first_seen_ip;
    private $claimed_by_registered_user_id;
    private $is_active;
    private $created_at;
    private $last_active_at;

    /**
     * Create a new anonymous user
     *
     * @param string $first_seen_ip IP address where the user was first seen
     * @param bool $is_active Whether the user is active
     * @return AnonymousUser
     */
    public static function create(string $first_seen_ip, bool $is_active = true): AnonymousUser
    {
        $db = Database::getInstance();

        $id = UuidHelper::generateUuid();
        $generated_username = UuidHelper::generateAnonymousUsername();

        $stmt = $db->prepare("
            INSERT INTO anonymous_users
            (id, generated_username, first_seen_ip, is_active)
            VALUES (?, ?, ?, ?)
        ");

        $stmt->execute([
            $id,
            $generated_username,
            $first_seen_ip,
            $is_active ? 1 : 0
        ]);

        return self::getById($id);
    }

    /**
     * Get an anonymous user by ID
     *
     * @param string $id User ID
     * @return AnonymousUser|null User object or null if not found
     */
    public static function getById(string $id): ?AnonymousUser
    {
        $db = Database::getInstance();

        $stmt = $db->prepare("SELECT * FROM anonymous_users WHERE id = ?");
        $stmt->execute([$id]);

        $userData = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$userData) {
            return null;
        }

        return self::createFromArray($userData);
    }

    /**
     * Get an anonymous user by generated username
     *
     * @param string $username Generated username
     * @return AnonymousUser|null User object or null if not found
     */
    public static function getByUsername(string $username): ?AnonymousUser
    {
        $db = Database::getInstance();

        $stmt = $db->prepare("SELECT * FROM anonymous_users WHERE generated_username = ?");
        $stmt->execute([$username]);

        $userData = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$userData) {
            return null;
        }

        return self::createFromArray($userData);
    }

    /**
     * Update the last_active_at timestamp for a user
     *
     * @param string $id User ID
     * @return bool True if successful
     */
    public static function updateLastActiveAt(string $id): bool
    {
        $db = Database::getInstance();

        $stmt = $db->prepare("UPDATE anonymous_users SET last_active_at = NOW() WHERE id = ?");
        return $stmt->execute([$id]);
    }

    /**
     * Claim an anonymous user by a registered user
     *
     * @param string $anonymousUserId Anonymous user ID
     * @param string $registeredUserId Registered user ID
     * @return bool True if successful
     */
    public static function claimByRegisteredUser(string $anonymousUserId, string $registeredUserId): bool
    {
        $db = Database::getInstance();

        $stmt = $db->prepare("
            UPDATE anonymous_users
            SET claimed_by_registered_user_id = ?
            WHERE id = ? AND claimed_by_registered_user_id IS NULL
        ");

        return $stmt->execute([$registeredUserId, $anonymousUserId]);
    }

    /**
     * Create an AnonymousUser object from an array of user data
     *
     * @param array $userData User data array
     * @return AnonymousUser
     */
    private static function createFromArray(array $userData): AnonymousUser
    {
        $user = new self();

        $user->id = $userData['id'];
        $user->generated_username = $userData['generated_username'];
        $user->first_seen_ip = $userData['first_seen_ip'];
        $user->claimed_by_registered_user_id = $userData['claimed_by_registered_user_id'];
        $user->is_active = (bool)$userData['is_active'];
        $user->created_at = $userData['created_at'];
        $user->last_active_at = $userData['last_active_at'];

        return $user;
    }

    // Getters
    public function getId(): string
    {
        return $this->id;
    }

    public function getGeneratedUsername(): string
    {
        return $this->generated_username;
    }

    public function getFirstSeenIp(): string
    {
        return $this->first_seen_ip;
    }

    public function getClaimedByRegisteredUserId(): ?string
    {
        return $this->claimed_by_registered_user_id;
    }

    public function isActive(): bool
    {
        return $this->is_active;
    }

    public function getCreatedAt(): string
    {
        return $this->created_at;
    }

    public function getLastActiveAt(): string
    {
        return $this->last_active_at;
    }

    public function isClaimed(): bool
    {
        return $this->claimed_by_registered_user_id !== null;
    }
}
