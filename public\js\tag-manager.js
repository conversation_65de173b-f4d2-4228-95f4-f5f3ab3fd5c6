/**
 * Tag Manager - Handles wallet tagging functionality
 * Uses CSS show/hide approach, no DOM manipulation
 */

const TagManager = {
    walletId: null,
    availableTags: [],
    isInitialized: false,

    /**
     * Initialize the tag manager
     */
    init(walletId) {
        if (this.isInitialized) return;

        this.walletId = walletId;
        this.setupEventListeners();
        this.loadInitialData();
        this.isInitialized = true;
    },

    /**
     * Setup event listeners for tag functionalities
     */
    setupEventListeners() {
        // Tag button click
        const tagButton = document.getElementById('tag-button');
        if (tagButton) {
            tagButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleTagDropdown();
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            const dropdown = document.getElementById('tag-dropdown-content');
            const tagButton = document.getElementById('tag-button');

            if (dropdown && !dropdown.contains(e.target) && !tagButton.contains(e.target)) {
                this.hideTagDropdown();
            }
        });

        // Tag badge clicks (event delegation)
        const availableTagsContainer = document.getElementById('available-tags');
        if (availableTagsContainer) {
            availableTagsContainer.addEventListener('click', (e) => {
                const tagBadge = e.target.closest('.tag-badge');
                if (tagBadge && !tagBadge.classList.contains('filled') && !tagBadge.classList.contains('disabled')) {
                    const tagId = parseInt(tagBadge.dataset.tagId);
                    const tagName = tagBadge.dataset.tagName;
                    this.showConfirmationPopover(tagId, tagName);
                }
            });
        }

        // Confirmation popover buttons
        const confirmBtn = document.getElementById('tag-confirm-btn');
        const cancelBtn = document.getElementById('tag-cancel-btn');
        const overlay = document.getElementById('tag-overlay');

        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                this.confirmAddTag();
            });
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.hideConfirmationPopover();
            });
        }

        if (overlay) {
            overlay.addEventListener('click', () => {
                this.hideConfirmationPopover();
            });
        }
    },

    /**
     * Load all tag data in a single API call
     */
    async loadInitialData() {
        try {
            const response = await fetch(`${baseUrl}api/tags/wallet-data/${this.walletId}`);
            const data = await response.json();

            if (data.success) {
                if (!data.tags_allowed) {
                    this.disableTagging();
                    return;
                }

                // Store the data
                this.availableTags = data.available_tags;

                // Render available tags and popular tags
                this.renderAvailableTags();
                this.renderPopularTags(data.popular_tags);
            } else {
                console.error('Failed to load tag data:', data.error);
            }
        } catch (error) {
            console.error('Error loading tag data:', error);
        }
    },

    /**
     * Render available tags in the dropdown
     */
    renderAvailableTags() {
        const container = document.getElementById('available-tags');
        if (!container) return;

        container.innerHTML = '';

        if (this.availableTags.length === 0) {
            container.innerHTML = '<span class="tag-placeholder">No tags available</span>';
            return;
        }

        this.availableTags.forEach(tag => {
            const badge = document.createElement('span');
            // Set class based on whether user has applied this tag
            badge.className = tag.user_applied ? 'tag-badge filled' : 'tag-badge outline';
            badge.dataset.tagId = tag.id;
            badge.dataset.tagName = tag.name;
            badge.textContent = tag.name;
            container.appendChild(badge);
        });
    },

    /**
     * Render popular tags in the sidebar
     */
    renderPopularTags(tags) {
        const container = document.querySelector('.tags-container');
        if (!container) return;

        container.innerHTML = '';

        if (tags.length === 0) {
            container.innerHTML = '<span class="tag-placeholder">No tags yet</span>';
            return;
        }

        tags.forEach(tag => {
            const tagElement = document.createElement('span');
            tagElement.className = 'popular-tag';
            tagElement.textContent = tag.name;

            if (tag.count > 1) {
                const counter = document.createElement('span');
                counter.className = 'tag-counter';
                counter.textContent = tag.count;
                tagElement.appendChild(counter);
            }

            container.appendChild(tagElement);
        });
    },

    /**
     * Toggle tag dropdown visibility
     */
    toggleTagDropdown() {
        const dropdown = document.getElementById('tag-dropdown-content');
        if (!dropdown) return;

        if (dropdown.classList.contains('show')) {
            this.hideTagDropdown();
        } else {
            this.showTagDropdown();
        }
    },

    /**
     * Show tag dropdown
     */
    showTagDropdown() {
        const dropdown = document.getElementById('tag-dropdown-content');
        if (dropdown) {
            dropdown.classList.add('show');
        }
    },

    /**
     * Hide tag dropdown
     */
    hideTagDropdown() {
        const dropdown = document.getElementById('tag-dropdown-content');
        if (dropdown) {
            dropdown.classList.remove('show');
        }
    },

    /**
     * Show confirmation popover for adding a tag
     */
    showConfirmationPopover(tagId, tagName) {
        const popover = document.getElementById('tag-confirmation-popover');
        const overlay = document.getElementById('tag-overlay');
        const tagDisplay = document.getElementById('confirmation-tag-name');
        const confirmBtn = document.getElementById('tag-confirm-btn');

        if (popover && overlay && tagDisplay && confirmBtn) {
            tagDisplay.textContent = tagName;
            confirmBtn.dataset.tagId = tagId;
            confirmBtn.dataset.tagName = tagName;

            overlay.classList.add('show');
            popover.classList.add('show');
        }
    },

    /**
     * Hide confirmation popover
     */
    hideConfirmationPopover() {
        const popover = document.getElementById('tag-confirmation-popover');
        const overlay = document.getElementById('tag-overlay');

        if (popover && overlay) {
            popover.classList.remove('show');
            overlay.classList.remove('show');
        }
    },

    /**
     * Confirm adding a tag
     */
    async confirmAddTag() {
        const confirmBtn = document.getElementById('tag-confirm-btn');
        if (!confirmBtn) return;

        const tagId = parseInt(confirmBtn.dataset.tagId);
        const tagName = confirmBtn.dataset.tagName;

        try {
            confirmBtn.classList.add('tag-loading');

            const response = await fetch(`${baseUrl}api/tags/add`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    tag_id: tagId,
                    wallet_id: this.walletId
                })
            });

            // Check if response is ok (status 200-299)
            if (!response.ok) {
                // Try to parse error response
                let errorMessage = 'Failed to add tag';
                try {
                    const errorData = await response.json();
                    errorMessage = errorData.error || errorMessage;
                } catch (e) {
                    // If JSON parsing fails, use status text
                    errorMessage = `Error ${response.status}: ${response.statusText}`;
                }

                if (window.toastManager) {
                    toastManager.error(errorMessage);
                }
                return;
            }

            const data = await response.json();

            if (data.success) {
                // Reload all tag data to refresh UI
                this.loadInitialData();
                this.hideConfirmationPopover();

                // Show success message
                if (window.toastManager) {
                    toastManager.success('Tag added successfully!');
                }
            } else {
                if (window.toastManager) {
                    toastManager.error(data.error || 'Failed to add tag');
                }
            }
        } catch (error) {
            console.error('Error adding tag:', error);
            if (window.toastManager) {
                toastManager.error('Network error. Please try again.');
            }
        } finally {
            confirmBtn.classList.remove('tag-loading');
        }
    },

    /**
     * Disable tagging functionality
     */
    disableTagging() {
        const tagButton = document.getElementById('tag-button');
        if (tagButton) {
            tagButton.disabled = true;
            tagButton.title = 'Tags are not allowed for this wallet';
        }
    }
};

// Export for use in other modules
window.TagManager = TagManager;
