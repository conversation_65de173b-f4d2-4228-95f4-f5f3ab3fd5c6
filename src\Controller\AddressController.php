<?php

namespace App\Controller;

use App\Core\AddressValidator;
use App\Model\Wallet;
use App\Model\Message;
// Remove Exception if not throwing it anymore
// use Exception;

class AddressController extends BaseController
{
    private AddressValidator $validator;
    private Wallet $walletModel;
    private Message $messageModel;

    public function __construct()
    {
        $this->validator = new AddressValidator();
        $this->walletModel = new Wallet();
        $this->messageModel = new Message();
    }

    /**
     * Displays the profile page structure for a given wallet address.
     * Validates the address and finds the existing wallet profile.
     * Messages are now loaded via AJAX.
     *
     * @param string $type The wallet type (from route parameter).
     * @param string $address The wallet address (from route parameter).
     * @return void
     */
    public function showProfile(string $type, string $address): void
    {
        // URL decoding
        $type = rawurldecode($type);
        $address = rawurldecode($address);

        // 1. Validate address format first
        if (!$this->validator->validate($type, $address)) {
            http_response_code(404);
            $this->render('pages/error', [
                'pageTitle' => 'Error 404',
                'errorCode' => 404,
                'errorMessage' => 'Invalid address format or type.'
            ]);
            return;
        }

        // 2. Find the existing wallet profile
        $wallet = $this->walletModel->findByTypeAndAddress($type, $address);

        // 3. Check if wallet was found
        if (!$wallet) {
            // Wallet not found in the database
            http_response_code(404);
            $this->render('pages/error', [
                'pageTitle' => 'Error 404',
                'errorCode' => 404,
                'errorMessage' => 'Wallet profile not found.'
            ]);
            return;
        }


        // 5. Render the profile view structure
        $this->render('pages/address_profile', [
            'pageTitle' => "Profile: " . htmlspecialchars($address, ENT_QUOTES, 'UTF-8'),
            'wallet' => $wallet
        ]);
    }

    /**
     * API endpoint to fetch messages for a wallet profile with pagination.
     * Returns JSON data.
     *
     * @param string $type The wallet type.
     * @param string $address The wallet address.
     * @return void
     */
    public function getMessagesJson(string $type, string $address): void
    {
        header('Content-Type: application/json');

        $type = rawurldecode($type);
        $address = rawurldecode($address);

        // Find the wallet
        $wallet = $this->walletModel->findByTypeAndAddress($type, $address);
        if (!$wallet) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Wallet not found']);
            exit;
        }

        // Get page number from query string, default to 1
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $page = max(1, $page); // Ensure page is at least 1
        $perPage = 10; // As per requirement

        // Fetch paginated messages using the new model method
        $data = $this->messageModel->findByWalletIdPaginated((int)$wallet['id'], $page, $perPage);

        // Calculate total pages
        $totalPages = ($data['totalMessages'] > 0) ? ceil($data['totalMessages'] / $perPage) : 0;

        echo json_encode([
            'success' => true,
            'messages' => $data['messages'],
            'pagination' => [
                'currentPage' => $page,
                'perPage' => $perPage,
                'totalMessages' => $data['totalMessages'],
                'totalPages' => $totalPages
            ]
        ]);
        exit;
    }

    // Legacy addMessageApi method removed - now handled by MessageController
}
