<div class="hero-block">
    <h1 class="main-heading">
        <span class="heading-with-badge">
            <span class="alpha-badge">ALPHA</span>
            CRYPTAG
        </span>
    </h1>
</div>

<p class="subheading">Public Profiles and Messages for Crypto Addresses</p>

<div class="search-section">
    <!-- Updated form for JS handling -->
    <form id="search-form" action="/search" method="POST">
        <!-- TODO: Add CSRF token field later if needed for API endpoint -->
        <!-- <input type="hidden" name="csrf_token" value="<?php // echo generate_csrf_token(); ?>"> -->

        <!-- Removed blockchain selector -->

        <div class="search-input-group">
            <input type="text" id="wallet-address-input" name="wallet_address" required minlength="26" placeholder="Enter BTC, ETH, or SOL address" aria-label="Wallet Address">
            <button type="submit">Search</button>
        </div>
    </form>
</div>

<div class="feature-boxes">
    <div class="card feature-box">
        <h3>DISCOVER</h3>
        <p>Find profiles associated with any BTC, ETH, or SOL address. See public tags and messages left by the community.</p>
    </div>
    <div class="card feature-box">
        <h3>COMMUNICATE</h3>
        <p>Leave public messages or tags on any address profile. Share information, warnings, or notes pseudonymously.</p>
    </div>
    <div class="card feature-box">
        <h3>CLAIM</h3>
        <p>Optionally verify ownership of an address to manage your public profile and messages (Feature coming soon).</p>
    </div>
</div>

<!-- Script will be included via main layout -->