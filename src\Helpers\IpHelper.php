<?php

namespace App\Helpers;

/**
 * IP Helper class
 * 
 * Provides functionality for handling IP addresses
 */
class IpHelper
{
    /**
     * Get the true client IP address
     * 
     * Prioritizes:
     * 1. CF-Connecting-IP (Cloudflare)
     * 2. X-Forwarded-For (other proxies)
     * 3. REMOTE_ADDR (direct connection)
     * 
     * @return string Client IP address
     */
    public static function getClientIp(): string
    {
        // Check for Cloudflare IP
        if (!empty($_SERVER['HTTP_CF_CONNECTING_IP'])) {
            return $_SERVER['HTTP_CF_CONNECTING_IP'];
        }
        
        // Check for X-Forwarded-For
        if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            // X-Forwarded-For can contain multiple IPs, take the first one
            $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            return trim($ips[0]);
        }
        
        // Fall back to REMOTE_ADDR
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Validate an IP address
     * 
     * @param string $ip IP address to validate
     * @return bool True if valid
     */
    public static function isValidIp(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }
    
    /**
     * Anonymize an IP address for privacy
     * 
     * For IPv4, the last octet is zeroed out
     * For IPv6, the last 80 bits are zeroed out
     * 
     * @param string $ip IP address to anonymize
     * @return string Anonymized IP address
     */
    public static function anonymizeIp(string $ip): string
    {
        if (!self::isValidIp($ip)) {
            return '0.0.0.0';
        }
        
        // Check if it's IPv4 or IPv6
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            // IPv4: Replace the last octet with 0
            return preg_replace('/\.\d+$/', '.0', $ip);
        } elseif (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
            // IPv6: Zero out the last 80 bits (last 5 hextets)
            $hex = bin2hex(inet_pton($ip));
            $hex = substr($hex, 0, 12) . str_repeat('0', 20);
            return inet_ntop(hex2bin($hex));
        }
        
        return '0.0.0.0';
    }
}
