<?php

namespace App\Model;

use App\Core\Database;
use App\Model\User; // Use the unified User model
use PDO;
use PDOException;
use Exception;

/**
 * Model for interacting with the 'messages' table.
 */
class Message
{
    private $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Finds all messages associated with a specific wallet ID, ordered by creation date.
     *
     * @param int $walletId The ID of the wallet.
     * @return array An array of message associative arrays, or an empty array if none found or on error.
     */
    public function findByWalletId(int $walletId): array
    {
        try {
            // Order by ID or created_at descending to show newest first
            $sql = "SELECT * FROM messages WHERE wallet_id = :wallet_id ORDER BY created_at DESC";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':wallet_id', $walletId, PDO::PARAM_INT);
            $stmt->execute();
            $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Enhance messages with user information
            foreach ($messages as &$message) {
                $senderInfo = $this->getSenderInfo($message);
                $message['sender_username'] = $senderInfo['username'];
                $message['sender_is_active'] = $senderInfo['is_active'];
                $message['sender_type'] = $message['is_anonymous_sender'] ? 'anonymous' : 'registered';
            }

            return $messages;
        } catch (PDOException $e) {
            error_log("Database Error finding messages by wallet ID: " . $e->getMessage());
            return []; // Return empty array on error
        }
    }

    /**
     * Finds messages associated with a specific wallet ID with pagination.
     *
     * @param int $walletId The ID of the wallet.
     * @param int $page The current page number (1-based).
     * @param int $perPage The number of messages per page.
     * @return array An array containing 'messages' and 'totalMessages', or ['messages' => [], 'totalMessages' => 0] on error.
     */
    public function findByWalletIdPaginated(int $walletId, int $page = 1, int $perPage = 10): array
    {
        // Ensure page and perPage are positive integers
        $page = max(1, $page);
        $perPage = max(1, $perPage);
        $offset = ($page - 1) * $perPage;

        $result = ['messages' => [], 'totalMessages' => 0];

        try {
            // Get total count first
            $countSql = "SELECT COUNT(*) FROM messages WHERE wallet_id = :wallet_id";
            $countStmt = $this->db->prepare($countSql);
            $countStmt->bindParam(':wallet_id', $walletId, PDO::PARAM_INT);
            $countStmt->execute();
            $totalMessages = (int)$countStmt->fetchColumn();

            if ($totalMessages > 0) {
                 $result['totalMessages'] = $totalMessages;

                // Fetch the paginated messages
                $sql = "SELECT * FROM messages
                        WHERE wallet_id = :wallet_id
                        ORDER BY created_at DESC
                        LIMIT :limit OFFSET :offset";
                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':wallet_id', $walletId, PDO::PARAM_INT);
                $stmt->bindParam(':limit', $perPage, PDO::PARAM_INT);
                $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
                $stmt->execute();
                $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Enhance messages with user information
                foreach ($messages as &$message) {
                    $senderInfo = $this->getSenderInfo($message);
                    $message['sender_username'] = $senderInfo['username'];
                    $message['sender_is_active'] = $senderInfo['is_active'];
                    $message['sender_type'] = $message['is_anonymous_sender'] ? 'anonymous' : 'registered';
                }

                $result['messages'] = $messages;
            }

            return $result;

        } catch (PDOException $e) {
            error_log("Database Error finding paginated messages by wallet ID: " . $e->getMessage());
            // Return empty result on error
            return ['messages' => [], 'totalMessages' => 0];
        }
    }


    /**
     * Saves a new message to the database.
     *
     * This method now has two signatures to maintain backward compatibility:
     * 1. Original: save(int $walletId, string $content, ?string $authorInfo = null)
     * 2. New: save(int $walletId, string $content, string $senderUserId, bool $isAnonymousSender, ?string $senderIp = null)
     *
     * @param int $walletId The ID of the wallet this message belongs to.
     * @param string $content The message content.
     * @param string|null $authorInfoOrSenderId Either the author info (old) or sender user ID (new)
     * @param bool|null $isAnonymousSender Whether the sender is anonymous (new parameter)
     * @param string|null $senderIp Optional IP address of the sender (new parameter)
     * @return int|false The ID of the newly inserted message, or false on failure.
     */
    public function save(
        int $walletId,
        string $content,
        $authorInfoOrSenderId = null,
        $isAnonymousSender = null,
        ?string $senderIp = null
    ): int|false
    {
        // Add explicit error logging
        error_log("Message::save called with walletId: {$walletId}, content length: " . strlen($content));

        // Basic validation
        if (empty(trim($content))) {
            error_log("Attempted to save an empty message for wallet ID: {$walletId}");
            return false;
        }
        if ($walletId <= 0) {
            error_log("Attempted to save message with invalid wallet ID: {$walletId}");
            return false;
        }

        try {
            // Determine which signature is being used
            if ($isAnonymousSender === null) {
                // Original signature: save(walletId, content, authorInfo)
                error_log("Using original signature with authorInfo: " . $authorInfoOrSenderId);
                $sql = "INSERT INTO messages (wallet_id, content, sender_ip, created_at)
                        VALUES (:wallet_id, :content, :sender_ip, NOW())";
                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':wallet_id', $walletId, PDO::PARAM_INT);
                $stmt->bindParam(':content', $content, PDO::PARAM_STR);
                $stmt->bindParam(':sender_ip', $authorInfoOrSenderId, PDO::PARAM_STR);
            } else {
                // New signature: save(walletId, content, senderUserId, isAnonymousSender, senderIp)
                error_log("Using new signature with senderUserId: " . $authorInfoOrSenderId);
                $sql = "INSERT INTO messages (wallet_id, content, sender_user_id, is_anonymous_sender, sender_ip, created_at)
                        VALUES (:wallet_id, :content, :sender_user_id, :is_anonymous_sender, :sender_ip, NOW())";
                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':wallet_id', $walletId, PDO::PARAM_INT);
                $stmt->bindParam(':content', $content, PDO::PARAM_STR);
                $stmt->bindParam(':sender_user_id', $authorInfoOrSenderId, PDO::PARAM_STR);
                $stmt->bindParam(':is_anonymous_sender', $isAnonymousSender, PDO::PARAM_BOOL);
                $stmt->bindParam(':sender_ip', $senderIp, PDO::PARAM_STR);
            }

            error_log("About to execute SQL: " . $sql);
            $stmt->execute();
            $lastId = (int)$this->db->lastInsertId();
            error_log("Message saved successfully with ID: " . $lastId);
            return $lastId;

        } catch (PDOException $e) {
            error_log("Database Error saving message: " . $e->getMessage());
            error_log("SQL State: " . $e->getCode());
            error_log("Stack trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Get sender information for a message
     *
     * @param array $message Message data from database
     * @return array Sender information with username and is_active status
     */
    public function getSenderInfo(array $message): array
    {
        $result = [
            'username' => 'Anonymous',
            'is_active' => true
        ];

        if (!isset($message['sender_user_id'])) {
            return $result;
        }

        try {
            // Get user info using the unified User model
            $senderUser = User::getById($message['sender_user_id'], $message['is_anonymous_sender'] ? 'anonymous_verified' : 'registered');
            if ($senderUser) {
                $result['username'] = ($senderUser->getType() === 'registered') ? $senderUser->getUsername() : $senderUser->getGeneratedUsername();
                $result['is_active'] = $senderUser->isActive();
            }

            // If user is not active, replace username with a generic message
            if (!$result['is_active']) {
                $result['username'] = 'User Deactivated';
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error getting sender info: " . $e->getMessage());
            return $result;
        }
    }

    // Add other necessary methods here (e.g., findById, update, delete if needed)
}
