<?php

namespace App\Helpers;

/**
 * UUID Helper class
 *
 * Provides UUID functionality that works both locally (using PHP implementation)
 * and on production (using the native UUID extension if available)
 */
class UuidHelper
{
    /**
     * Check if the native UUID extension is available
     *
     * @return bool True if the native UUID extension is available
     */
    public static function isNativeExtensionAvailable(): bool
    {
        return function_exists('uuid_create');
    }

    /**
     * Generate a UUID v4 (random)
     *
     * @return string UUID string
     */
    public static function generateUuid(): string
    {
        // If the native UUID extension is available, use it
        if (self::isNativeExtensionAvailable()) {
            return uuid_create(UUID_TYPE_RANDOM);
        }

        // Otherwise, use a PHP implementation
        return self::generateUuidPhp();
    }

    /**
     * Generate a UUID v4 (random) using PHP only
     *
     * @return string UUID string
     */
    private static function generateUuidPhp(): string
    {
        // Generate 16 random bytes
        $data = random_bytes(16);

        // Set version to 0100 (UUID v4)
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40);

        // Set bits 6-7 to 10
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

        // Format the UUID string
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }

    /**
     * Check if a UUID is valid
     *
     * @param string $uuid UUID to validate
     * @return bool True if the UUID is valid
     */
    public static function isValid(string $uuid): bool
    {
        if (self::isNativeExtensionAvailable()) {
            return uuid_is_valid($uuid);
        }

        // UUID pattern: 8-4-4-4-12 hexadecimal digits
        $pattern = '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i';
        return (bool) preg_match($pattern, $uuid);
    }

    /**
     * Generate a random anonymous username in the format "a" followed by 10 random digits
     *
     * @return string Random anonymous username
     */
    public static function generateAnonymousUsername(): string
    {
        $randomDigits = str_pad(mt_rand(0, 9999999999), 10, '0', STR_PAD_LEFT);
        return "a" . $randomDigits;
    }
}
