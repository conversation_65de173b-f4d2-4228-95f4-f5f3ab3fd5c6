<?php

namespace App\Model;

use App\Core\Database;
use PDO;
use PDOException;

/**
 * Model for interacting with the 'tags' table.
 */
class Tag
{
    private PDO $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Get all available tags for a specific item type.
     *
     * @param string $itemType The item type ('wallet_profile' or 'message')
     * @return array Array of tag data
     */
    public function getAvailableTagsForItemType(string $itemType): array
    {
        try {
            $sql = "SELECT id, name, description, restricted_to_item_type 
                    FROM tags 
                    WHERE restricted_to_item_type IS NULL OR restricted_to_item_type = :item_type
                    ORDER BY name ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':item_type', $itemType, PDO::PARAM_STR);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Database Error getting available tags: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get a tag by its ID.
     *
     * @param int $tagId
     * @return array|false Tag data or false if not found
     */
    public function findById(int $tagId): array|false
    {
        try {
            $sql = "SELECT * FROM tags WHERE id = :id LIMIT 1";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $tagId, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Database Error finding tag by ID: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get a tag by its name.
     *
     * @param string $name
     * @return array|false Tag data or false if not found
     */
    public function findByName(string $name): array|false
    {
        try {
            $sql = "SELECT * FROM tags WHERE name = :name LIMIT 1";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':name', $name, PDO::PARAM_STR);
            $stmt->execute();
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Database Error finding tag by name: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create a new tag.
     *
     * @param string $name Tag name
     * @param string|null $description Optional description
     * @param string|null $restrictedToItemType Optional restriction to item type
     * @return int|false Tag ID if created successfully, false on failure
     */
    public function create(string $name, ?string $description = null, ?string $restrictedToItemType = null): int|false
    {
        try {
            $sql = "INSERT INTO tags (name, description, restricted_to_item_type, created_at) 
                    VALUES (:name, :description, :restricted_to_item_type, NOW())";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':name', $name, PDO::PARAM_STR);
            $stmt->bindParam(':description', $description, PDO::PARAM_STR);
            $stmt->bindParam(':restricted_to_item_type', $restrictedToItemType, PDO::PARAM_STR);
            $stmt->execute();
            
            return (int)$this->db->lastInsertId();
        } catch (PDOException $e) {
            error_log("Database Error creating tag: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if a tag can be used for a specific item type.
     *
     * @param int $tagId
     * @param string $itemType
     * @return bool
     */
    public function canBeUsedForItemType(int $tagId, string $itemType): bool
    {
        $tag = $this->findById($tagId);
        if (!$tag) {
            return false;
        }

        // If tag has no restriction, it can be used for any item type
        if ($tag['restricted_to_item_type'] === null) {
            return true;
        }

        // If tag has restriction, it must match the item type
        return $tag['restricted_to_item_type'] === $itemType;
    }
}
