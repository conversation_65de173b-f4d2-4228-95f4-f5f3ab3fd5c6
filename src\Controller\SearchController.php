<?php

namespace App\Controller;

use App\Core\AddressValidator;
use App\Model\Wallet;
// No longer need Request class directly here as we read php://input

class SearchController extends BaseController
{
    private AddressValidator $validator;
    private Wallet $walletModel;
    private array $supportedTypes = ['btc', 'eth', 'sol']; // Define supported types

    public function __construct()
    {
        $this->validator = new AddressValidator();
        $this->walletModel = new Wallet(); // Instantiate Wallet model here
        // No need for session_start here as we aren't using session messages
    }

    /**
     * Handles the wallet search AJAX request.
     * Detects address type, finds or creates the wallet, and returns JSON.
     *
     * @return void Outputs JSON response and exits.
     */
    public function handleSearch(): void
    {
        // Set content type to JSON for all responses from this endpoint
        header('Content-Type: application/json');

        // Check if it's a POST request
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405); // Method Not Allowed
            echo json_encode(['success' => false, 'error' => 'Method Not Allowed']);
            exit;
        }

        // Read the JSON input from the request body
        $input = json_decode(file_get_contents('php://input'), true);

        // Basic check for the presence of wallet_address
        if (!isset($input['wallet_address']) || !is_string($input['wallet_address'])) {
            http_response_code(400); // Bad Request
            echo json_encode(['success' => false, 'error' => 'Missing or invalid wallet_address parameter.']);
            exit;
        }

        $address = trim($input['wallet_address']);

        if (empty($address)) {
            http_response_code(400); // Bad Request
            echo json_encode(['success' => false, 'error' => 'Wallet address cannot be empty.']);
            exit;
        }

        // --- Address Type Detection ---
        $detectedType = null;
        foreach ($this->supportedTypes as $type) {
            if ($this->validator->validate($type, $address)) {
                $detectedType = $type;
                break; // Found the first matching type
            }
        }

        // --- Handle based on detection result ---
        if ($detectedType === null) {
            // No supported type matched the address format
            http_response_code(400); // Bad Request
            echo json_encode(['success' => false, 'error' => 'Invalid or unsupported wallet address format.']);
            exit;
        }

        // --- Find or Create Wallet ---
        $wallet = $this->walletModel->findOrCreateByTypeAndAddress($detectedType, $address);

        if ($wallet) {
            // Wallet found or created successfully
            // Use the getBaseUrl from BaseController now
            $redirectUrl = $this->getBaseUrl() . 'address/' . rawurlencode($detectedType) . '/' . rawurlencode($address);
            echo json_encode(['success' => true, 'redirectUrl' => $redirectUrl]);
            exit;
        } else {
            // Database error during findOrCreate
            http_response_code(500); // Internal Server Error
            error_log("SearchController: Failed findOrCreate for Type={$detectedType}, Address={$address}"); // Log the error server-side
            echo json_encode(['success' => false, 'error' => 'Could not process the request. Please try again later.']);
            exit;
        }
    }

    // Removed the local getBaseUrl method
/**
     * Handles the /@walletAddress vanity URL.
     * Detects address type, finds or creates the wallet, and redirects.
     *
     * @param string $walletAddress The wallet address from the URL.
     * @return void Performs a redirect and exits.
     */
    public function handleVanityUrl(string $walletAddress): void
    {
        $address = trim(rawurldecode($walletAddress)); // Decode URL component and trim whitespace

        if (empty($address)) {
            // If address is empty after decoding, redirect to homepage with an error
            $this->redirect($this->getBaseUrl() . '?error=empty_address_in_url');
            exit;
        }

        // --- Address Type Detection (similar to handleSearch) ---
        $detectedType = null;
        foreach ($this->supportedTypes as $type) {
            if ($this->validator->validate($type, $address)) {
                $detectedType = $type;
                break; // Found the first matching type
            }
        }

        if ($detectedType === null) {
            // No supported type matched the address format
            // Redirect to homepage with an error
            $this->redirect($this->getBaseUrl() . '?error=invalid_or_unsupported_address_in_url');
            exit;
        }

        // --- Find or Create Wallet (similar to handleSearch) ---
        $wallet = $this->walletModel->findOrCreateByTypeAndAddress($detectedType, $address);

        if ($wallet) {
            // Wallet found or created successfully
            // Construct the profile URL and redirect
            $profileUrl = $this->getBaseUrl() . 'address/' . rawurlencode($detectedType) . '/' . rawurlencode($address);
            $this->redirect($profileUrl);
            exit;
        } else {
            // Database error during findOrCreate or other issue
            error_log("SearchController::handleVanityUrl: Failed findOrCreate for Type={$detectedType}, Address={$address}");
            // Redirect to homepage with a generic error
            $this->redirect($this->getBaseUrl() . '?error=processing_error_in_url_search');
            exit;
        }
    }
}