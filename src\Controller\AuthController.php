<?php

namespace App\Controller;

use App\Core\Database;
use App\Helpers\IpHelper;
use App\Helpers\JwtHelper;
use App\Helpers\UuidHelper;
use App\Model\RateLimit;
use App\Model\User; // Use the unified User model
use App\Service\CaptchaService;
use Exception;

/**
 * AuthController
 *
 * Handles user authentication operations
 */
class AuthController extends BaseController
{
    private $captchaService;

    public function __construct()
    {
        $this->captchaService = new CaptchaService();
    }

    /**
     * Register a new user
     */
    public function register()
    {
        // If user is already logged in, prevent registration
        if ($this->currentUser && $this->currentUser->getType() === 'registered' && $this->currentUser->isActive()) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Already logged in'], 409);
            return;
        }

        // Only accept POST requests
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->sendJsonResponse(['success' => false, 'error' => 'Method not allowed'], 405);
            return;
        }

        // Get client IP
        $clientIp = IpHelper::getClientIp();

        // Check if IP is in denylist
        if (RateLimit::isIpInDenylist($clientIp)) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Too many requests. Please try again later.'], 429);
            return;
        }

        // Get and validate input
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data) {
            $data = $_POST;
        }

        $username = $data['username'] ?? '';
        $email = $data['email'] ?? '';
        $password = $data['password'] ?? '';
        $cfTurnstileResponse = $data['cf_turnstile_response'] ?? '';


        // Validate input
        if (empty($username) || empty($email) || empty($password)) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Username, email, and password are required'], 400);
            return;
        }

        // Validate username format
        $usernameValidation = User::validateUsername($username);
        if (!$usernameValidation['valid']) {
            $this->sendJsonResponse(['success' => false, 'error' => $usernameValidation['error']], 400);
            return;
        }

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Invalid email format'], 400);
            return;
        }

        if (strlen($password) < 8) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Password must be at least 8 characters long'], 400);
            return;
        }

        if (empty($cfTurnstileResponse)) {
            $this->sendJsonResponse(['success' => false, 'error' => 'CAPTCHA verification is required'], 400);
            return;
        }

        // Check registration rate limit
        if (RateLimit::isRegistrationLimitExceeded($clientIp)) {
            RateLimit::addIpToDenylist($clientIp);
            $this->sendJsonResponse(['success' => false, 'error' => 'Too many registration attempts. Please try again later.'], 429);
            return;
        }

        // Record the registration attempt
        RateLimit::recordAttempt($clientIp, RateLimit::TYPE_IP_REGISTRATION_ATTEMPT);

        // Verify CAPTCHA using CaptchaService
        if (!$this->captchaService->verifyTurnstileToken($cfTurnstileResponse, $clientIp)) {
            $this->sendJsonResponse(['success' => false, 'error' => 'CAPTCHA verification failed. Please check server logs for details.'], 400);
            return;
        }

        // Check if username or email already exists
        if (User::usernameExists($username)) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Username already exists'], 409);
            return;
        }

        if (User::emailExists($email)) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Email already exists'], 409);
            return;
        }

        try {
            // Hash password
            $passwordHash = password_hash($password, PASSWORD_BCRYPT);

            // Create user
            $user = User::createRegisteredUser($username, $email, $passwordHash, $clientIp);

            // If current user is an anonymous user, claim their account securely
            if ($this->currentUser && $this->currentUser->getType() === 'anonymous_verified') {
                $anonymousUserId = $this->currentUser->getId();
                $claimResult = User::claimAnonymousUser($anonymousUserId, $user->getId());
                if ($claimResult) {
                    error_log("Successfully claimed anonymous user {$anonymousUserId} for registered user {$user->getId()}");
                } else {
                    error_log("Failed to claim anonymous user {$anonymousUserId} for registered user {$user->getId()}");
                }
            }

            // Generate JWT and CSRF token
            $jwt = JwtHelper::generateRegisteredUserJwt($user->getId(), $user->getUsername(), $user->isActive());
            $csrfToken = JwtHelper::generateCsrfToken();

            // Set cookies
            JwtHelper::setAuthCookie($jwt, JwtHelper::REGISTERED_USER_EXPIRY);
            JwtHelper::setCsrfCookie($csrfToken, JwtHelper::REGISTERED_USER_EXPIRY);

            // Return success response
            $this->sendJsonResponse([
                'success' => true,
                'user_id' => $user->getId(),
                'username' => $user->getUsername()
            ], 201);
        } catch (Exception $e) {
            error_log("Registration Exception: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->sendJsonResponse(['success' => false, 'error' => 'Registration failed due to a server error.'], 500);
        }
    }

    /**
     * Login a user
     */
    public function login()
    {
        // If user is already logged in, prevent login
        if ($this->currentUser && $this->currentUser->getType() === 'registered' && $this->currentUser->isActive()) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Already logged in'], 409);
            return;
        }

        // Only accept POST requests
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->sendJsonResponse(['success' => false, 'error' => 'Method not allowed'], 405);
            return;
        }

        // Get client IP
        $clientIp = IpHelper::getClientIp();

        // Check if IP is in denylist
        if (RateLimit::isIpInDenylist($clientIp)) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Too many requests. Please try again later.'], 429);
            return;
        }

        // Get and validate input
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data) {
            $data = $_POST;
        }

        $emailOrUsername = $data['email_or_username'] ?? '';
        $password = $data['password'] ?? '';
        $cfTurnstileResponse = $data['cf_turnstile_response'] ?? '';
        $linkAnonymousAccount = $data['link_anonymous_account'] ?? false;

        // Validate input
        if (empty($emailOrUsername) || empty($password)) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Email/username and password are required'], 400);
            return;
        }

        if (empty($cfTurnstileResponse)) {
            $this->sendJsonResponse(['success' => false, 'error' => 'CAPTCHA verification is required'], 400);
            return;
        }

        // Check login rate limits
        if (RateLimit::isIpLoginLimitExceeded($clientIp)) {
            RateLimit::addIpToDenylist($clientIp);
            $this->sendJsonResponse(['success' => false, 'error' => 'Too many login attempts from this IP. Please try again later.'], 429);
            return;
        }

        if (RateLimit::isUserLoginLimitExceeded($emailOrUsername)) {
            RateLimit::addIpToDenylist($clientIp);
            $this->sendJsonResponse(['success' => false, 'error' => 'Too many login attempts for this account. Please try again later.'], 429);
            return;
        }

        // Get user by email or username
        $user = User::getByUsernameOrEmail($emailOrUsername);

        // Verify CAPTCHA using CaptchaService
        if (!$this->captchaService->verifyTurnstileToken($cfTurnstileResponse, $clientIp)) {
            $this->sendJsonResponse(['success' => false, 'error' => 'CAPTCHA verification failed. Please check server logs for details.'], 400);
            return;
        }

        // If user not found or not active, record failed attempt and return error
        // Also ensure it's a registered user, as anonymous users cannot log in this way
        if (!$user || !$user->isActive() || $user->getType() !== 'registered') {
            RateLimit::recordAttempt($clientIp, RateLimit::TYPE_IP_LOGIN_ATTEMPT);
            RateLimit::recordAttempt($emailOrUsername, RateLimit::TYPE_USER_LOGIN_ATTEMPT);
            $this->sendJsonResponse(['success' => false, 'error' => 'Invalid credentials or account inactive'], 401);
            return;
        }

        // Verify password
        if (!password_verify($password, $user->getPasswordHash())) {
            RateLimit::recordAttempt($clientIp, RateLimit::TYPE_IP_LOGIN_ATTEMPT);
            RateLimit::recordAttempt($emailOrUsername, RateLimit::TYPE_USER_LOGIN_ATTEMPT);
            $this->sendJsonResponse(['success' => false, 'error' => 'Invalid credentials'], 401);
            return;
        }

        try {
            // Update last active at
            User::updateLastActiveAt($user->getId(), $user->getType());

            // If anonymous user linking is requested and current user is anonymous, claim securely
            if ($linkAnonymousAccount && $this->currentUser && $this->currentUser->getType() === 'anonymous_verified') {
                $anonymousUserId = $this->currentUser->getId();
                $claimResult = User::claimAnonymousUser($anonymousUserId, $user->getId());
                if ($claimResult) {
                    error_log("Successfully claimed anonymous user {$anonymousUserId} for logged-in user {$user->getId()}");
                } else {
                    error_log("Failed to claim anonymous user {$anonymousUserId} for logged-in user {$user->getId()}");
                }
            }

            // Generate JWT and CSRF token
            $jwt = JwtHelper::generateRegisteredUserJwt($user->getId(), $user->getUsername(), $user->isActive());
            $csrfToken = JwtHelper::generateCsrfToken();

            // Set cookies
            error_log("=== LOGIN SUCCESS - SETTING COOKIES ===");
            error_log("JWT token length: " . strlen($jwt));
            error_log("CSRF token length: " . strlen($csrfToken));

            $authCookieSet = JwtHelper::setAuthCookie($jwt, JwtHelper::REGISTERED_USER_EXPIRY);
            $csrfCookieSet = JwtHelper::setCsrfCookie($csrfToken, JwtHelper::REGISTERED_USER_EXPIRY);

            error_log("Auth cookie set result: " . ($authCookieSet ? 'success' : 'failed'));
            error_log("CSRF cookie set result: " . ($csrfCookieSet ? 'success' : 'failed'));
            error_log("Headers sent: " . (headers_sent() ? 'yes' : 'no'));

            // Return success response
            $this->sendJsonResponse([
                'success' => true,
                'user_id' => $user->getId(),
                'username' => $user->getUsername()
            ]);
        } catch (Exception $e) {
            error_log("Login Exception: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->sendJsonResponse(['success' => false, 'error' => 'Login failed due to a server error.'], 500);
        }
    }

    /**
     * Logout a user
     */
    public function logout()
    {
        // Clear auth cookies
        JwtHelper::clearAuthCookies();

        // Return success response
        $this->sendJsonResponse(['success' => true]);
    }



    // Removed the private verifyCaptcha method as it's now in CaptchaService
}
