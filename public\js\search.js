document.addEventListener('DOMContentLoaded', () => {
    const searchForm = document.getElementById('search-form');
    const addressInput = document.getElementById('wallet-address-input');
    const submitButton = searchForm ? searchForm.querySelector('button[type="submit"]') : null;

    if (!searchForm || !addressInput || !submitButton) {
        // console.error('Search form elements not found. Ensure IDs are correct: #search-form, #wallet-address-input, and the form has a submit button.');
        return; // Stop if essential elements are missing
    }

    searchForm.addEventListener('submit', async (event) => {
        event.preventDefault(); // Prevent traditional form submission

        // Show loading state
        submitButton.disabled = true; // Disable button during request
        const originalButtonText = submitButton.textContent;
        submitButton.innerHTML = '<span class="loading"></span>'; // Add loading spinner

        const address = addressInput.value.trim();

        if (!address) {
            toastManager.error('Please enter a wallet address.');
            submitButton.disabled = false;
            submitButton.innerHTML = originalButtonText;
            return;
        }

        try {
            const response = await fetch(baseUrl + 'search', { // Or use '/api/search' if you prefer a dedicated API route
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json', // Send data as JSON
                    'Accept': 'application/json' // Expect JSON response
                },
                body: JSON.stringify({ wallet_address: address }) // Send address in JSON body
            });

            const data = await response.json(); // Always try to parse JSON

            if (response.ok && data.success && data.redirectUrl) {
                // Success: Redirect to the profile page
                window.location.href = data.redirectUrl;
                // No need to re-enable button here as we are navigating away
            } else {
                // Handle errors (including non-ok responses or ok responses with success: false)
                toastManager.error(data.error || 'An unknown error occurred.');
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
            }

        } catch (error) {
            console.error('Search fetch error:', error);
            toastManager.error('A network error occurred. Please try again.');
            submitButton.disabled = false;
            submitButton.innerHTML = originalButtonText;
        }
    });

    // Add input validation and feedback
    addressInput.addEventListener('input', () => {
        // Clear error when user starts typing again
        if (errorMessageElement.style.display !== 'none') {
            errorMessageElement.textContent = '';
            errorMessageElement.style.display = 'none';
        }

        // Add visual feedback based on input length
        if (addressInput.value.length >= 26) {
            addressInput.classList.add('valid-input');
            addressInput.classList.remove('invalid-input');
        } else if (addressInput.value.length > 0) {
            addressInput.classList.add('invalid-input');
            addressInput.classList.remove('valid-input');
        } else {
            addressInput.classList.remove('valid-input');
            addressInput.classList.remove('invalid-input');
        }
    });
});