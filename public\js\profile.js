/**
 * Escapes HTML special characters to prevent XSS attacks
 * @param {string} unsafe The unsafe string that might contain HTML
 * @returns {string} The escaped safe string
 */
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

document.addEventListener('DOMContentLoaded', () => {
    const messageList = document.getElementById('message-list');
    const paginationControls = document.getElementById('pagination-controls');
    const messageForm = document.getElementById('add-message-form');
    const messageTextarea = messageForm?.querySelector('textarea[name="message_content"]'); // Find textarea within the form
    const walletInfoContainer = document.querySelector('[data-wallet-id]'); // Find element with wallet ID
    const autoRefreshIndicator = document.querySelector('.auto-refresh-indicator'); // Find auto-refresh indicator

    // Check if essential elements exist. If not, exit silently.
    if (!messageList || !paginationControls || !messageForm || !walletInfoContainer || !messageTextarea) {
        // console.log('Profile elements not found. Exiting profile.js.'); // Optional: uncomment for debugging
        return;
    }

    // Check if baseUrl is defined (should be from main.php)
    if (typeof baseUrl === 'undefined') {
        console.error('Global `baseUrl` variable is not defined. Aborting script.');
        return;
    }

    const walletId = walletInfoContainer.dataset.walletId;
    const walletType = walletInfoContainer.dataset.walletType; // Assuming you add data-wallet-type
    const walletAddress = walletInfoContainer.dataset.walletAddress; // Assuming you add data-wallet-address

    if (!walletId || !walletType || !walletAddress) {
        console.error('Wallet ID, Type, or Address data attribute is missing.');
        // Don't necessarily abort here, maybe just disable form submission?
        // For now, let it continue but log the error.
    }

    let autoRefreshIntervalId = null;
    let currentPage = 1; // Keep track of the current page
    let isSubmitting = false; // Track if a message is being submitted
    let currentMessages = []; // Keep track of current messages for seamless refresh

    // --- Core Functions ---

    /**
     * Fetches messages from the API for a given page.
     * @param {number} page Page number to fetch.
     * @param {boolean} isAutoRefresh Whether this fetch is from auto-refresh
     */
    async function fetchMessages(page = 1, isAutoRefresh = false) {
        console.log(`Fetching messages for page ${page}...`); // Keep non-debug logs if desired

        // Show loading state if first load or empty list, but not during auto-refresh
        if (!isAutoRefresh && (messageList.innerHTML === '' || messageList.querySelector('.loading-container'))) {
            messageList.innerHTML = `
                <div class="loading-container">
                    <span class="loading"></span>
                    <span>Loading messages...</span>
                </div>
            `;
        }

        // Prepend baseUrl to the API path
        const apiUrl = `${baseUrl}api/address/${encodeURIComponent(walletType)}/${encodeURIComponent(walletAddress)}/messages?page=${page}`;
        try {
            const response = await fetch(apiUrl);
            if (!response.ok) {
                 // Log the response body for more details on 404 or other errors
                const errorText = await response.text();
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();

            if (data.success) {
                // If this is an auto-refresh and we're on page 1, handle it differently
                if (isAutoRefresh && page === 1) {
                    updateMessagesSeamlessly(data.messages);
                } else {
                    renderMessages(data.messages);
                    currentMessages = [...data.messages]; // Store current messages
                }

                renderPagination(data.pagination.currentPage, data.pagination.totalPages);
                currentPage = data.pagination.currentPage; // Update current page tracker

                // Manage auto-refresh: only active for page 1
                if (currentPage === 1) {
                    startAutoRefresh();
                    if (autoRefreshIndicator) {
                        autoRefreshIndicator.style.display = 'flex';
                    }
                } else {
                    stopAutoRefresh();
                    if (autoRefreshIndicator) {
                        autoRefreshIndicator.style.display = 'none';
                    }
                }
            } else {
                console.error('API Error fetching messages:', data.error);
                if (!isAutoRefresh) {
                    messageList.innerHTML = '<p class="error-message">Could not load messages.</p>';
                    toastManager.error('Could not load messages.');
                }
                if (autoRefreshIndicator) {
                    autoRefreshIndicator.style.display = 'none';
                }
            }
        } catch (error) {
            console.error('Fetch Error:', error);
            if (!isAutoRefresh) {
                messageList.innerHTML = '<p class="error-message">Could not load messages due to a network error.</p>';
                toastManager.error('Could not load messages due to a network error.');
            }
            stopAutoRefresh(); // Stop refresh on error
            if (autoRefreshIndicator) {
                autoRefreshIndicator.style.display = 'none';
            }
        }
    }

    /**
     * Updates messages seamlessly during auto-refresh by only adding new messages.
     * @param {Array} newMessages Array of message objects from the API.
     */
    function updateMessagesSeamlessly(newMessages) {
        if (newMessages.length === 0) {
            if (messageList.children.length === 0) {
                messageList.innerHTML = '<p class="no-messages">No messages yet. Be the first to leave a message!</p>';
            }
            return;
        }

        // If there are no current messages, just render them all
        if (currentMessages.length === 0 || messageList.children.length === 0) {
            renderMessages(newMessages);
            currentMessages = [...newMessages];
            return;
        }

        // Use MessageRenderer to add new messages with proper styling
        currentMessages = MessageRenderer.addNewMessages(newMessages, currentMessages, messageList);
    }

    /**
     * Renders messages into the message list container using template-based approach.
     * @param {Array} messages Array of message objects.
     */
    function renderMessages(messages) {
        MessageRenderer.renderMessages(messages, messageList, true);
    }



    /**
     * Renders pagination controls.
     * @param {number} currentPage Current active page.
     * @param {number} totalPages Total number of pages.
     */
    function renderPagination(currentPage, totalPages) {
        paginationControls.innerHTML = ''; // Clear existing controls

        if (totalPages <= 1) {
            return; // No pagination needed for 1 or 0 pages
        }

        const paginationList = document.createElement('ul');
        paginationList.classList.add('pagination'); // For styling

        // Previous Button
        if (currentPage > 1) {
            const prevItem = document.createElement('li');
            const prevLink = document.createElement('button');
            prevLink.textContent = 'Previous';
            prevLink.dataset.page = currentPage - 1;
            prevLink.addEventListener('click', handlePaginationClick);
            prevItem.appendChild(prevLink);
            paginationList.appendChild(prevItem);
        }

        // Page Number Buttons
        // Show first page, current page, and last page with ellipsis
        const pagesToShow = [];

        // Always show first page
        pagesToShow.push(1);

        // Show ellipsis after first page if needed
        if (currentPage > 3) {
            pagesToShow.push('...');
        }

        // Show page before current if not first page or ellipsis
        if (currentPage > 2) {
            pagesToShow.push(currentPage - 1);
        }

        // Show current page if not first page
        if (currentPage !== 1) {
            pagesToShow.push(currentPage);
        }

        // Show page after current if not last page
        if (currentPage < totalPages - 1) {
            pagesToShow.push(currentPage + 1);
        }

        // Show ellipsis before last page if needed
        if (currentPage < totalPages - 2) {
            pagesToShow.push('...');
        }

        // Always show last page if not first page
        if (totalPages > 1) {
            pagesToShow.push(totalPages);
        }

        // Remove duplicates
        const uniquePages = [...new Set(pagesToShow)];

        // Create page buttons
        uniquePages.forEach(page => {
            const pageItem = document.createElement('li');

            if (page === '...') {
                // Ellipsis
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                ellipsis.classList.add('ellipsis');
                pageItem.appendChild(ellipsis);
            } else {
                // Page number
                const pageLink = document.createElement('button');
                pageLink.textContent = page;
                pageLink.dataset.page = page;
                if (page === currentPage) {
                    pageLink.disabled = true; // Disable current page button
                    pageItem.classList.add('active');
                }
                pageLink.addEventListener('click', handlePaginationClick);
                pageItem.appendChild(pageLink);
            }

            paginationList.appendChild(pageItem);
        });

        // Next Button
        if (currentPage < totalPages) {
            const nextItem = document.createElement('li');
            const nextLink = document.createElement('button');
            nextLink.textContent = 'Next';
            nextLink.dataset.page = currentPage + 1;
            nextLink.addEventListener('click', handlePaginationClick);
            nextItem.appendChild(nextLink);
            paginationList.appendChild(nextItem);
        }

        paginationControls.appendChild(paginationList);
    }

    /**
     * Handles clicks on pagination buttons.
     * @param {Event} event The click event.
     */
    function handlePaginationClick(event) {
        event.preventDefault();
        const targetPage = parseInt(event.target.dataset.page, 10);
        if (!isNaN(targetPage) && targetPage !== currentPage) {
            // Disable all pagination buttons during page change
            const buttons = paginationControls.querySelectorAll('button');
            buttons.forEach(btn => btn.disabled = true);

            // Add loading state to clicked button
            event.target.innerHTML = '<span class="loading"></span>';

            // Fetch the new page
            fetchMessages(targetPage);
        }
    }

    /**
     * Handles the message form submission via AJAX.
     * @param {Event} event The form submission event.
     */
    async function submitMessage(event) {
        event.preventDefault(); // Prevent traditional form submission

        if (isSubmitting) return; // Prevent multiple submissions
        isSubmitting = true;

        stopAutoRefresh(); // Stop refresh while submitting

        // Get submit button and original text
        const submitButton = messageForm.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.textContent;

        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="loading"></span> Posting...';

        // Get message content
        const messageContent = messageTextarea.value?.trim();

        // Client-side validation
        if (!messageContent) {
            // Show error toast
            toastManager.error('Message cannot be empty.');

            // Reset button
            submitButton.disabled = false;
            submitButton.innerHTML = originalButtonText;
            isSubmitting = false;
            startAutoRefreshIfNeeded(); // Restart refresh if needed
            return;
        }

        // Check user type to determine authentication method
        const isRegistered = Auth.isRegisteredUser();

        let requestData = {
            message_content: messageContent,
            wallet_id: walletId // Include wallet_id for legacy validation
        };

        if (isRegistered) {
            // Registered user - use CSRF token
        } else {
            // Anonymous user - require CAPTCHA
            const turnstileResponse = CaptchaManager.getResponse();
            if (!turnstileResponse) {
                // Show CAPTCHA requirement
                CaptchaManager.show();

                // Reset button
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
                isSubmitting = false;
                startAutoRefreshIfNeeded();
                return;
            }
            requestData.cf_turnstile_response = turnstileResponse;
        }

        // Prepare fetch options
        const submitUrl = `${baseUrl}api/address/${encodeURIComponent(walletType)}/${encodeURIComponent(walletAddress)}/add-message`;
        let fetchOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData),
            credentials: 'same-origin'
        };

        if (isRegistered) {
            // Add CSRF token for registered users
            fetchOptions = Auth.addCsrfToFetchOptions(fetchOptions);
        }

        try {
            const response = await fetch(submitUrl, fetchOptions);

            // Try to parse JSON regardless of status for potential error messages
            let result = {};
            try {
                 result = await response.json();
            } catch (jsonError) {
                 // If JSON parsing fails, try to get text for debugging
                 const errorText = await response.text();
                 console.error(`Failed to parse response as JSON. Response text: ${errorText}`);
                 console.error('JSON parse error:', jsonError);
                 result = { success: false, error: 'Received non-JSON response from server.' };
            }

            if (response.ok && result.success) {
                // Show success toast
                toastManager.success('Message posted successfully!');

                // Clear the textarea with animation
                messageTextarea.classList.add('success-highlight');
                setTimeout(() => {
                    messageTextarea.value = ''; // Clear the textarea
                    messageTextarea.classList.remove('success-highlight');
                }, 500);

                // Reset CAPTCHA only for anonymous users
                if (!isRegistered) {
                    CaptchaManager.reset();
                }

                fetchMessages(1); // Refresh to show the new message on page 1
                // Auto-refresh will be restarted by fetchMessages if it lands on page 1
            } else {
                // Show error toast
                toastManager.error(`Error: ${result.error || 'Please try again.'}`);

                // Reset CAPTCHA only for anonymous users
                if (!isRegistered) {
                    CaptchaManager.reset();
                }

                startAutoRefreshIfNeeded(); // Restart refresh if needed
            }
        } catch (error) {
            console.error('Submit Fetch Error:', error);

            // Show error toast
            toastManager.error('Network error. Please try again.');

            // Reset CAPTCHA only for anonymous users on network error
            if (!isRegistered) {
                CaptchaManager.reset();
            }

            startAutoRefreshIfNeeded(); // Restart refresh if needed
        } finally {
            // Reset button state
            submitButton.disabled = false;
            submitButton.innerHTML = originalButtonText;
            isSubmitting = false;
        }
    }

    // --- CAPTCHA Functions (now handled by CaptchaManager) ---

    /**
     * Initialize CAPTCHA based on user type
     * Only anonymous users need CAPTCHA, registered users use CSRF
     */
    function initializeCaptchaIfNeeded() {
        const isRegistered = Auth.isRegisteredUser();

        if (!isRegistered) {
            CaptchaManager.show(false); // Don't show toast on initial load
        }
    }

    /** Starts the 10-second auto-refresh interval for page 1 and comments tab. */
    function startAutoRefresh() {
        if (autoRefreshIntervalId === null) { // Only start if not already running
            console.log('Starting auto-refresh...'); // Keep non-debug logs if desired

            // Show auto-refresh indicator
            if (autoRefreshIndicator) {
                autoRefreshIndicator.style.display = 'flex';
            }

            autoRefreshIntervalId = setInterval(() => {
                // Only fetch if we are still on page 1 AND comments tab is active
                if (currentPage === 1 && isCommentsTabActive()) {
                    console.log('Auto-refreshing page 1...'); // Keep non-debug logs if desired
                    fetchMessages(1, true); // Pass true to indicate this is an auto-refresh
                } else if (!isCommentsTabActive()) {
                    // If not on comments tab, stop auto-refresh
                    stopAutoRefresh();
                }
            }, 10000); // 10 seconds
        }
    }

    /** Stops the auto-refresh interval. */
    function stopAutoRefresh() {
        if (autoRefreshIntervalId !== null) {
            // console.log('Stopping auto-refresh...'); // Keep non-debug logs if desired
            clearInterval(autoRefreshIntervalId);
            autoRefreshIntervalId = null;

            // Hide auto-refresh indicator if not on page 1
            if (autoRefreshIndicator && currentPage !== 1) {
                autoRefreshIndicator.style.display = 'none';
            }
        }
    }

    /** Restarts auto-refresh only if the current page is 1 and comments tab is active. */
    function startAutoRefreshIfNeeded() {
        if (currentPage === 1 && isCommentsTabActive()) {
            startAutoRefresh();
        }
    }

    // --- Initialization ---
    if (messageForm) { // Add a check just in case
        messageForm.addEventListener('submit', submitMessage);

        // Add textarea focus/blur effects
        if (messageTextarea) {
            messageTextarea.addEventListener('focus', () => {
                messageTextarea.parentNode.classList.add('focused');
            });

            messageTextarea.addEventListener('blur', () => {
                messageTextarea.parentNode.classList.remove('focused');
            });
        }
    }

    // Initialize CaptchaManager and MessageRenderer
    CaptchaManager.init(messageForm);
    MessageRenderer.init();

    // Initialize TagManager if on wallet profile page
    if (window.TagManager && walletId) {
        TagManager.init(walletId);
    }

    // Initial load of messages (page 1)
    fetchMessages(1);



    // Initialize CAPTCHA for anonymous users on page load
    initializeCaptchaIfNeeded();

    // Add tab navigation functionality with CSS-based switching
    function setupTabNavigation() {
        const tabLinks = document.querySelectorAll('.tab-link');

        tabLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault(); // Prevent default anchor behavior

                const tabName = link.dataset.tab;
                switchToTab(tabName);

                // Update active tab link
                tabLinks.forEach(lnk => lnk.classList.remove('active'));
                link.classList.add('active');
            });
        });
    }

    /**
     * Switch to a specific tab using CSS visibility (no DOM manipulation)
     * @param {string} tabName - Name of the tab to switch to
     */
    function switchToTab(tabName) {
        // Hide all tab content using CSS
        const allTabContent = document.querySelectorAll('.tab-content');
        allTabContent.forEach(tab => {
            tab.style.display = 'none';
        });

        // Show the selected tab
        const targetTab = document.getElementById(`${tabName}-tab`);
        if (targetTab) {
            targetTab.style.display = 'block';
        }

        // Handle auto-refresh and data loading based on tab
        if (tabName === 'comments') {
            // Refresh messages when switching to comments tab
            fetchMessages(currentPage);
            // Start auto-refresh for comments (only if on page 1)
            startAutoRefreshIfNeeded();
        } else {
            // Stop auto-refresh when leaving comments tab
            stopAutoRefresh();
        }

        console.log(`Switched to ${tabName} tab`);
    }

    /**
     * Check if the comments tab is currently active/visible
     * @returns {boolean} True if comments tab is visible
     */
    function isCommentsTabActive() {
        const commentsTab = document.getElementById('comments-tab');
        return commentsTab && commentsTab.style.display !== 'none';
    }



    // Add copy address functionality
    function setupCopyAddress() {
        const copyButton = document.querySelector('.copy-address');
        if (copyButton) {
            copyButton.addEventListener('click', () => {
                const address = walletAddress;

                // Function to handle successful copy
                const handleSuccess = () => {
                    // Show success toast notification
                    if (window.toastManager) {
                        toastManager.success(`Address copied to clipboard: ${address.substring(0, 10)}...`);
                    }

                    // Also show visual feedback on the button
                    const buttonIcon = copyButton.querySelector('.button-icon');
                    const originalHTML = buttonIcon.innerHTML;

                    // Change icon to check mark (using check-circle from Lucide list)
                    if (typeof lucide !== 'undefined') {
                        buttonIcon.innerHTML = '<i data-lucide="check-circle"></i>';
                        lucide.createIcons();
                    }

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        buttonIcon.innerHTML = originalHTML;
                        if (typeof lucide !== 'undefined') {
                            lucide.createIcons();
                        }
                    }, 2000);
                };

                // Function to handle copy failure
                const handleError = (err) => {
                    // Show error toast notification
                    if (window.toastManager) {
                        toastManager.error('Failed to copy address to clipboard');
                    }
                    console.error('Failed to copy address:', err);
                };

                // Try to use the Clipboard API first
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    navigator.clipboard.writeText(address)
                        .then(handleSuccess)
                        .catch(handleError);
                } else {
                    // Fallback for browsers that don't support the Clipboard API
                    try {
                        // Create a temporary textarea element
                        const textarea = document.createElement('textarea');
                        textarea.value = address;

                        // Make the textarea out of viewport
                        textarea.style.position = 'fixed';
                        textarea.style.left = '-999999px';
                        textarea.style.top = '-999999px';
                        document.body.appendChild(textarea);

                        // Select and copy
                        textarea.focus();
                        textarea.select();
                        // Note: execCommand is deprecated but used here as a fallback for older browsers
                        const successful = document.execCommand('copy');

                        // Remove the textarea
                        document.body.removeChild(textarea);

                        if (successful) {
                            handleSuccess();
                        } else {
                            handleError(new Error('execCommand returned false'));
                        }
                    } catch (err) {
                        handleError(err);
                    }
                }
            });
        }
    }

    // Add explorer link functionality
    function setupExplorerLink() {
        const explorerButton = document.querySelector('.view-explorer');
        if (explorerButton) {
            explorerButton.addEventListener('click', () => {
                let explorerUrl;
                let explorerName;

                switch(walletType.toLowerCase()) {
                    case 'eth':
                        explorerUrl = `https://etherscan.io/address/${walletAddress}`;
                        explorerName = 'Etherscan';
                        break;
                    case 'btc':
                        explorerUrl = `https://btcscan.org/address/${walletAddress}`;
                        explorerName = 'BTCScan';
                        break;
                    case 'sol':
                        explorerUrl = `https://solscan.io/account/${walletAddress}`;
                        explorerName = 'Solscan';
                        break;
                    default:
                        // Show error toast notification instead of alert
                        if (window.toastManager) {
                            toastManager.error('Explorer not available for this wallet type');
                        } else {
                            alert('Explorer not available for this wallet type');
                        }
                        return;
                }

                // Show toast notification
                if (window.toastManager) {
                    toastManager.info(`Opening ${explorerName} in a new tab...`);
                }

                // Show visual feedback on the button
                const buttonIcon = explorerButton.querySelector('.button-icon');
                if (buttonIcon) {
                    const originalHTML = buttonIcon.innerHTML;

                    // Change icon to external link with animation
                    if (typeof lucide !== 'undefined') {
                        buttonIcon.innerHTML = '<i data-lucide="external-link" class="icon-pulse"></i>';
                        lucide.createIcons();

                        // Add a quick pulse animation class
                        const iconElement = buttonIcon.querySelector('i');
                        if (iconElement) {
                            iconElement.classList.add('icon-pulse');
                        }

                        // Reset button after 1 second
                        setTimeout(() => {
                            buttonIcon.innerHTML = originalHTML;
                            if (typeof lucide !== 'undefined') {
                                lucide.createIcons();
                            }
                        }, 1000);
                    }
                }

                // Open explorer in a new tab
                window.open(explorerUrl, '_blank');
            });
        }
    }



    // Setup all functionality
    function setupAllFunctionality() {
        setupTabNavigation();
        setupCopyAddress();
        setupExplorerLink();
    }

    // Initial setup
    setupAllFunctionality();

    // Also ensure setup is called when DOM is loaded (in case script runs before DOM is ready)
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', setupAllFunctionality);
    }
});
