// Registration page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('register-form');
    const registerButton = document.getElementById('register-button');
    const originalButtonText = registerButton.textContent.trim();

    // Get base URL from global variable (set by PHP)
    const baseUrl = window.baseUrl || '/cryptag/';

    // Initialize CAPTCHA manager
    if (window.CaptchaManager) {
        CaptchaManager.init(registerForm);
        CaptchaManager.show(false); // Show CAPTCHA without toast
    }

    registerForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const username = document.getElementById('username').value;
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const passwordConfirm = document.getElementById('password_confirm').value;
        const turnstileResponse = CaptchaManager ? CaptchaManager.getResponse() : document.getElementById('cf-turnstile-response').value;

        // Validate form
        if (!username || !email || !password) {
            toastManager.error('All fields are required');
            return;
        }

        if (password.length < 8) {
            toastManager.error('Password must be at least 8 characters long');
            return;
        }

        if (password !== passwordConfirm) {
            toastManager.error('Passwords do not match');
            return;
        }

        if (!turnstileResponse) {
            toastManager.error('Please complete the CAPTCHA');
            return;
        }

        // Show loading state
        registerButton.disabled = true;
        registerButton.innerHTML = '<span class="loading"></span> Registering...';

        // Send registration request
        fetch(baseUrl + 'api/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                email: email,
                password: password,
                cf_turnstile_response: turnstileResponse
            }),
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success toast
                toastManager.success('Registration successful! Redirecting...');

                // Redirect to home page on success after a short delay
                setTimeout(() => {
                    window.location.href = baseUrl;
                }, 1000);
            } else {
                // Show error toast
                toastManager.error(data.error || 'Registration failed');

                // Reset button
                registerButton.disabled = false;
                registerButton.innerHTML = originalButtonText;

                // Reset CAPTCHA
                if (CaptchaManager) {
                    CaptchaManager.reset();
                }
            }
        })
        .catch(error => {
            toastManager.error('An error occurred. Please try again.');

            // Reset button
            registerButton.disabled = false;
            registerButton.innerHTML = originalButtonText;

            // Reset CAPTCHA
            if (CaptchaManager) {
                CaptchaManager.reset();
            }
        });
    });
});
