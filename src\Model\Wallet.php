<?php

namespace App\Model;

use App\Core\Database;
use App\Core\AddressValidator; // Assuming AddressValidator might be needed here
use PDO;
use PDOException;
use Exception; // Or custom exceptions

/**
 * Model for interacting with the 'wallets' table.
 */
class Wallet
{
    private PDO $db;
    private AddressValidator $validator; // Inject or instantiate validator

    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->validator = new AddressValidator(); // Simple instantiation for now; consider Dependency Injection
    }

    /**
     * Finds a wallet by its type and address, or creates it if it doesn't exist and the address is valid.
     *
     * @param string $type The wallet type (e.g., 'btc', 'eth', 'sol').
     * @param string $address The wallet address.
     * @return array|false Wallet data as an associative array if found or created, false on failure or invalid address.
     */
    public function findOrCreateByTypeAndAddress(string $type, string $address): array|false
    {
        $type = strtolower($type); // Ensure consistent type casing

        // First, try to find the wallet
        $wallet = $this->findByTypeAndAddress($type, $address);

        if ($wallet) {
            return $wallet; // Found existing wallet
        }

        // Not found, validate the address before attempting creation
        if (!$this->validator->validate($type, $address)) {
            // Optionally log this attempt or throw a specific validation exception
            error_log("Attempted to create wallet with invalid address: Type={$type}, Address={$address}");
            return false; // Invalid address format, do not create
        }

        // Address is valid and wallet doesn't exist, proceed with creation
        try {
            $sql = "INSERT INTO wallets (type, address, created_at, updated_at) VALUES (:type, :address, NOW(), NOW())";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':type', $type, PDO::PARAM_STR);
            $stmt->bindParam(':address', $address, PDO::PARAM_STR);
            $stmt->execute();

            // Get the newly created wallet's ID
            $id = $this->db->lastInsertId();

            // Return the data of the newly created wallet
            return $this->findById((int)$id);

        } catch (PDOException $e) {
            // Log the error securely
            error_log("Database Error creating wallet: " . $e->getMessage());
            // Depending on policy, you might return false or re-throw a custom exception
            return false;
        }
    }

    /**
     * Finds a wallet by its type and address.
     *
     * @param string $type
     * @param string $address
     * @return array|false Wallet data or false if not found.
     */
    public function findByTypeAndAddress(string $type, string $address): array|false
    {
        try {
            $sql = "SELECT * FROM wallets WHERE type = :type AND address = :address LIMIT 1";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':type', $type, PDO::PARAM_STR);
            $stmt->bindParam(':address', $address, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Database Error finding wallet by type/address: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Finds a wallet by its ID.
     *
     * @param int $id
     * @return array|false Wallet data or false if not found.
     */
    public function findById(int $id): array|false
    {
        try {
            $sql = "SELECT * FROM wallets WHERE id = :id LIMIT 1";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Database Error finding wallet by ID: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if tags are allowed for this wallet.
     *
     * @param int $walletId
     * @return bool
     */
    public function areTagsAllowed(int $walletId): bool
    {
        try {
            $sql = "SELECT are_tags_allowed FROM wallets WHERE id = :id LIMIT 1";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $walletId, PDO::PARAM_INT);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? (bool)$result['are_tags_allowed'] : false;
        } catch (PDOException $e) {
            error_log("Database Error checking tags allowed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get wallet with tag permission info.
     *
     * @param int $id
     * @return array|false Wallet data with tag permission or false if not found.
     */
    public function findByIdWithTagInfo(int $id): array|false
    {
        try {
            $sql = "SELECT *, are_tags_allowed FROM wallets WHERE id = :id LIMIT 1";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Database Error finding wallet with tag info: " . $e->getMessage());
            return false;
        }
    }

    // Add other necessary methods here (e.g., update, delete if needed)
}

