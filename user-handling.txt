This is a comprehensive technical specification for implementing the messaging and user account features for `crypt.ag`, incorporating all the elements we've discussed.
This document is intended to be detailed enough for an AI agent or developer to use as a blueprint.
**Document Version:** 1.4 (Final consolidation, all user preferences incorporated)
**Date:** May 19, 2023

**1. Overview & Goals**

The primary goal is to allow users to send messages to wallet addresses anonymously.
Additionally, the system will support two types of user accounts: auto-generated "anonymous verified" accounts (for users who post messages after CAPTCHA verification) and fully "registered" user accounts. Users can be deactivated (banned) via an `is_active` flag in their respective user tables. Messages and user records are intended to be archived and not hard-deleted. The `last_active_at` timestamp in user tables will track user activity, updated periodically by application logic.
Robust security measures, including CAPTCHA (Cloudflare Turnstile), CSRF protection (Double Submit Cookie Pattern), and multi-layered rate limiting (APCu Quick Denylist + MySQL Primary Rate Limiting), are critical.

**2. Core Entities & Data Models (MySQL)**

The following tables will be used. The `wallets` table is assumed to be existing (as per the `schema.sql` context). `registered_users`, `anonymous_users`, and `rate_limit_attempts` are new. The `messages` table (also assumed existing from `schema.sql`) will be modified for sender identification and to rename `author_info` to `sender_ip`.

  * **`wallets` Table (Existing Reference):**
      * `id` (INT AUTO_INCREMENT PRIMARY KEY)
      * `type` (VARCHAR(10) NOT NULL COMMENT 'e.g., btc, eth, sol')
      * `address` (VARCHAR(255) NOT NULL)
      * `created_at` (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
      * `updated_at` (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)
      * UNIQUE KEY `idx_type_address` (`type`, `address`)

  * **`registered_users` Table (New):**
      * `id` (CHAR(36), Primary Key, COMMENT 'UUID generated by PHP application e.g., using ramsey/uuid')
      * `username` (VARCHAR(50), Unique, Not Null)
      * `email` (VARCHAR(255), Unique, Not Null)
      * `password_hash` (VARCHAR(255), Not Null, COMMENT 'Hash generated by password_hash() using BCRYPT or Argon2')
      * `is_active` (BOOLEAN, Not Null, DEFAULT TRUE, COMMENT 'TRUE if user is active/not banned, FALSE otherwise')
      * `registration_ip` (VARCHAR(45), Nullable, COMMENT 'IP address used during registration')
      * `created_at` (TIMESTAMP, Default CURRENT_TIMESTAMP)
      * `updated_at` (TIMESTAMP, Default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)
      * `last_active_at` (TIMESTAMP, NOT NULL, DEFAULT CURRENT_TIMESTAMP, COMMENT 'Timestamp of the last known user activity, updated by application logic')

  * **`anonymous_users` Table (New):**
      * `id` (CHAR(36), Primary Key, COMMENT 'UUID generated by PHP application e.g., using ramsey/uuid')
      * `generated_username` (VARCHAR(50), Not Null, COMMENT 'Format: "anon-" followed by 10 random digits, e.g., "anon-1947362934"')
      * `first_seen_ip` (VARCHAR(45), Nullable)
      * `claimed_by_registered_user_id` (CHAR(36), Nullable, Foreign Key to `registered_users.id` ON DELETE SET NULL)
      * `is_active` (BOOLEAN, Not Null, DEFAULT TRUE, COMMENT 'TRUE if user is active/not banned, FALSE otherwise')
      * `created_at` (TIMESTAMP, Default CURRENT_TIMESTAMP)
      * `last_active_at` (TIMESTAMP, NOT NULL, DEFAULT CURRENT_TIMESTAMP, COMMENT 'Timestamp of the last known user activity, updated by application logic')

  * **`rate_limit_attempts` Table (New):**
      * `id` (BIGINT UNSIGNED, Primary Key, AUTO_INCREMENT)
      * `identifier` (VARCHAR(255), Not Null, COMMENT 'Stores IP address or UserID (CHAR(36) UUID)')
      * `identifier_type` (ENUM('ip_captcha_validation', 'user_message_post', 'ip_registration_attempt', 'user_login_attempt'), Not Null)
      * `attempt_timestamp` (TIMESTAMP, Not Null, Default CURRENT_TIMESTAMP)
      * INDEX `idx_rate_limit_check` (`identifier`, `identifier_type`, `attempt_timestamp`)

  * **Modifications to existing `messages` Table (adapting user's `schema.sql`):**
      * `id` (INT AUTO_INCREMENT PRIMARY KEY)
      * `wallet_id` (INT NOT NULL, COMMENT 'FK to wallets.id - the target wallet/profile for the message')
      * `content` (TEXT NOT NULL)
      * `sender_user_id` (CHAR(36), NOT NULL, COMMENT 'UUID of the sender, from anonymous_users or registered_users table')
      * `is_anonymous_sender` (BOOLEAN, NOT NULL, COMMENT 'TRUE if sender_user_id refers to anonymous_users, FALSE if registered_users')
      * `sender_ip` (VARCHAR(45), Nullable, COMMENT 'IP Address of the sender (renamed from author_info)')
      * `created_at` (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
      * `updated_at` (TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)
      * (The Foreign Key `FOREIGN KEY (wallet_id) REFERENCES wallets(id) ON DELETE CASCADE` from user's schema remains)
**3. User Authentication & Session Management (JWTs)**

  * **JWT Standard Claims:**
      * `iss` (Issuer): Site domain (e.g., `https://crypt.ag`)
      * `aud` (Audience): Site domain (e.g., `https://crypt.ag`)
      * `iat` (Issued At): Unix timestamp of token issuance.
      * `nbf` (Not Before): Unix timestamp, token not valid before this time.
      * `exp` (Expiration Time): Unix timestamp of token expiry.
      * `jti` (JWT ID): Unique identifier for the token (e.g., a UUID).

  * **JWT Custom Claims:**
      * `sub` (Subject): User's unique ID (`RegisteredUserID` or `AnonymousUserID` - both are UUIDs).
      * `username`: User's display name (`chosen_username` or `GeneratedRandomUsername`).
      * `type`: User type (String: "registered" or "anonymous_verified").
      * `active`: (BOOLEAN) User's `is_active` status from the database at the time of JWT issuance.
      * `lar`: (Integer, Unix Timestamp) "Last Activity Recorded" - Timestamp indicating when the application last updated the `last_active_at` field in the database for this user's session.

  * **JWT Payloads:**
      * **Registered User JWT:**
          * `sub`: `RegisteredUserID`
          * `username`: `chosen_username`
          * `type`: "registered"
          * `active`: User's `is_active` status (e.g., `true`)
          * `exp`: `iat` + 6 hours (21600 seconds)
          * `lar`: `iat` (on initial login/JWT issuance). Subsequently updated when `last_active_at` in DB is updated.
      * **Anonymous Verified User JWT:**
          * `sub`: `AnonymousUserID`
          * `username`: `GeneratedRandomUsername`
          * `type`: "anonymous_verified"
          * `active`: User's `is_active` status (e.g., `true`)
          * `exp`: `iat` + 30 days (2592000 seconds)
          * `lar`: `iat` (on initial JWT issuance). Subsequently updated when `last_active_at` in DB is updated.

  * **JWT Handling:**
      * **Issuance:** Upon successful login (for registered users) or first successful CAPTCHA-verified post (for anonymous users). Only issue JWTs for users whose `is_active` status in the database is `TRUE`. Set `lar` to current time (`iat`).
      * **Signing Algorithm:** RS256 or ES256 (asymmetric) is recommended for security. HS256 (symmetric) is simpler if the secret key is very strong, kept highly secure, and not exposed client-side.
      * **Storage:** Transmit JWTs to the client via an **HttpOnly, Secure cookie with `SameSite=Lax`**. Setting `Path=/` is also recommended.
      * **Cookie Name (JWT):** Recommended: `__Host-auth_token` (requires `Secure` flag, `Path=/`, and no `Domain` attribute; ensures cookie is tied to the hostname). Fallback: `auth_token`.
      * **Validation (Backend):** On each authenticated request, validate the JWT's signature, expiry (`exp`), not before (`nbf`), issuer (`iss`), and audience (`aud`). Extract claims.
      * **Periodic `last_active_at` Update & JWT Refresh:**
          This logic applies to authenticated requests.
          1. After validating the current JWT, extract `userID = jwt_payload->sub`, `userType = jwt_payload->type`, and `larTimestamp = jwt_payload->lar`.
          2. Get current server time: `currentTime = time();` (Unix timestamp).
          3. If `(currentTime - larTimestamp) >= 600` (10 minutes):
              a. Determine table: `anonymous_users` if `userType == "anonymous_verified"`, else `registered_users`.
              b. SQL: `UPDATE user_table SET last_active_at = NOW() WHERE id = :userID;`
              c. Re-issue the JWT: Create a new JWT with the same `sub`, `username`, `type`, and `active` status (re-fetch `is_active` from DB if critical, or trust JWT's `active` claim for this refresh). Set new `iat` (`currentTime`), new `exp` (e.g., `currentTime` + original duration), and updated `lar` (`currentTime`).
              d. Send new JWT back to client via `Set-Cookie` header.

  * **CSRF Protection (Double Submit Cookie Pattern):**
      * **CSRF Token Cookie:** When the JWT auth cookie is set/refreshed, also set/refresh a separate cookie:
          * Name: `__Host-csrf_token` (or `csrf_token`).
          * Value: A cryptographically secure random string (e.g., 32 bytes, base64 encoded).
          * Attributes: **NOT HttpOnly**, `Secure`, `SameSite=Lax`, `Path=/`.
          * Expiry: Same as the JWT auth cookie or session-based.
      * **Client-Side:** JavaScript reads this cookie value and includes it in a custom HTTP header (e.g., `X-CSRF-Token`) for all state-changing requests (POST, PUT, DELETE, PATCH).
      * **Server-Side Validation:** For state-changing requests, the backend verifies:
        1. Valid JWT is present.
        2. The `X-CSRF-Token` header is present.
        3. The value from the `X-CSRF-Token` header matches the value in the `__Host-csrf_token` cookie.
        If any check fails, reject with HTTP 403 Forbidden.

**4. CAPTCHA Integration (Cloudflare Turnstile)**

  * **Client-Side:** Integrate Cloudflare Turnstile widget (using your Site Key) into forms requiring CAPTCHA (e.g., first anonymous post, registration form). Upon successful challenge completion, Turnstile injects a token into a hidden form field, typically named `cf-turnstile-response`.
  * **Server-Side Token Verification:**
      * Your backend receives the `cf-turnstile-response` token.
      * Make a server-to-server POST request to `https://challenges.cloudflare.com/turnstile/v0/siteverify`.
      * **Request Parameters:**
          * `secret`: Your Cloudflare Turnstile **Secret Key** (`YOUR_CLOUDFLARE_TURNSTILE_SECRET_KEY`).
          * `response`: The `cf-turnstile-response` token value received from the client.
          * `remoteip`: The end user's IP address (optional but recommended by Cloudflare for better fraud detection).
      * **Response Handling:** Cloudflare returns JSON. Check `json_response.success`. If `true`, CAPTCHA is valid. Log any `error-codes` if `false`.

**5. Rate Limiting Strategy (Multi-Layered)**

  * **Layer 1: APCu Quick Denylist (Pre-filter):**
      * Requires APCu enabled on the PHP server (`extension=apcu.so` and `apc.enabled=1` in `php.ini`).
      * **Purpose:** Temporarily block IPs identified as abusive by MySQL rate limits, reducing DB load.
      * **Key Structure:** `denylist_ip_<CLIENT_IP_ADDRESS>` (e.g., `denylist_ip_1.2.3.4`).
      * **Value Stored:** `1` or `true`.
      * **TTL (Time To Live):** 300 seconds (5 minutes). Set when an IP is added.
      * **Check Logic (PHP):** At the beginning of relevant request handlers:
        ```php
        // $clientIp = get_true_client_ip();
        // if (function_exists('apcu_exists') && apcu_exists('denylist_ip_' . $clientIp)) {
        //     http_response_code(429);
        //     echo json_encode(["error" => "Too many requests. Please try again shortly."]);
        //     exit;
        // }
        ```
      * **Add Logic (PHP):** When a MySQL rate limit is definitively exceeded by an IP:
        ```php
        // if (function_exists('apcu_store')) {
        //     apcu_store('denylist_ip_' . $clientIp, 1, 300); // Block for 300s
        // }
        ```

  * **Layer 2: MySQL Primary Rate Limiting (using `rate_limit_attempts` table):**
      * **A. IP-based for CAPTCHA Validation Attempts (Applied *before* calling Turnstile's `siteverify`):**
          * `identifier`: `$clientIp`
          * `identifier_type`: 'ip_captcha_validation'
          * **Threshold & Window:** 10 attempts per IP per 10 minutes.
          * **Logic:**
            1.  SQL: `SELECT COUNT(*) FROM rate_limit_attempts WHERE identifier = :identifier AND identifier_type = 'ip_captcha_validation' AND attempt_timestamp > (NOW() - INTERVAL 10 MINUTE);`
            2.  If count >= 10, add `clientIp` to APCu Quick Denylist, log, return HTTP 429.
            3.  Else, `INSERT INTO rate_limit_attempts (identifier, identifier_type) VALUES (:identifier, 'ip_captcha_validation');`. Proceed to Turnstile verification.
      * **B. IP-based for Registration Attempts (Applied *before* creating user record):**
          * `identifier`: `$clientIp`
          * `identifier_type`: 'ip_registration_attempt'
          * **Threshold & Window:** 5 attempts per IP per 1 hour.
          * **Logic:** Similar to CAPTCHA validation. If exceeded, add `clientIp` to APCu Denylist.
      * **C. User-based for Message Posting (Applied *after* JWT/CSRF validation & user `is_active` check):**
          * `identifier`: `UserID` (from JWT `sub` claim - `AnonymousUserID` or `RegisteredUserID`).
          * `identifier_type`: 'user_message_post'.
          * **Thresholds & Window:**
              * Anonymous Verified Users: 5 messages per hour.
              * Registered Users: 20 messages per hour.
          * **Logic:**
            1.  SQL: `SELECT COUNT(*) FROM rate_limit_attempts WHERE identifier = :identifier AND identifier_type = 'user_message_post' AND attempt_timestamp > (NOW() - INTERVAL 1 HOUR);`
            2.  If count >= respective threshold, log, (optionally add `clientIp` associated with this `UserID` to APCu Denylist if frequently hitting limit), return HTTP 429.
            3.  Else, allow post, then `INSERT INTO rate_limit_attempts (identifier, identifier_type) VALUES (:identifier, 'user_message_post');` after successful message save.
      * **D. User/IP-based for Login Attempts:**
          * `identifier_user`: `$username_or_email` (lowercase for consistency).
          * `identifier_ip`: `$clientIp`.
          * `identifier_type_user`: 'user_login_attempt'.
          * `identifier_type_ip`: 'ip_login_attempt' (can be a separate type if needed, or just use 'user_login_attempt' with IP as identifier).
          * **Thresholds & Window:**
              * 5 failed login attempts per username per 15 minutes.
              * 10 failed login attempts per IP per 15 minutes.
          * **Logic:** Upon failed login, `INSERT` for both username and IP identifiers. Check counts before validating password. If exceeded, add `clientIp` to APCu Denylist.
      * **Database Cleanup:** Implement a script (e.g., cron job if possible, or triggered by occasional admin action) to run `DELETE FROM rate_limit_attempts WHERE attempt_timestamp < NOW() - INTERVAL 3 DAY;`.

**6. API Endpoint Specifications & Logic Flows**
All API responses should be JSON. Obtain the true client IP (e.g., via `CF-Connecting-IP` if using Cloudflare, then `X-Forwarded-For`, then `REMOTE_ADDR`).

  * **`POST /api/messages`** (Replaces previous more complex endpoint for adding messages)
      * **Request Body (JSON or Form Data):**
          * `target_wallet_address` (String, Not Null, e.g., "**********************************")
          * `wallet_type` (String, Not Null, e.g., "btc", "eth")
          * `message_content` (String, Not Null, Max length 5000 chars)
          * `cf_turnstile_response` (String, Required if no valid active JWT, or if the JWT `active` claim is false)
      * **Headers:** `X-CSRF-Token` (Required if JWT auth cookie is present and valid)
      * **Logic Flow:**
        1.  Extract `target_wallet_address`, `wallet_type`, `message_content`, `cf_turnstile_response` from request.
        2.  Get True Client IP (`$clientIp`).
        3.  **APCu Quick Denylist Check:** If `$clientIp` is in APCu denylist, return HTTP 429.
        4.  Initialize `$userID = null`, `$userType = null`, `$jwtValidAndActive = false`.
        5.  Check for JWT auth cookie. If present, validate it (signature, expiry, iss, aud).
            * If valid, extract `jwt_payload`. Set `$userID = jwt_payload->sub`, `$userType = jwt_payload->type`.
            * Check `jwt_payload->active`. If `true`, set `$jwtValidAndActive = true`.
            * Perform periodic `last_active_at` DB update & JWT refresh logic if needed (based on `jwt_payload->lar`).
        6.  **If NOT `$jwtValidAndActive`:** (No JWT, invalid JWT, or JWT `active` claim is false)
            * If `cf_turnstile_response` is missing, return HTTP 400 ("CAPTCHA required").
            * **MySQL IP Rate Limit (CAPTCHA Validation):** Apply 'ip_captcha_validation' limit for `$clientIp`. If exceeded, add to APCu, return HTTP 429.
            * If IP OK, record attempt in `rate_limit_attempts`.
            * Verify `cf_turnstile_response` with Cloudflare. If invalid, return HTTP 400 ("Invalid CAPTCHA").
            * If Turnstile valid:
                * Generate `AnonymousUserID` (UUID via `ramsey/uuid`).
                * Generate `GeneratedRandomUsername` (e.g., "anon-" + 10 random digits: `$randomDigits = str_pad(mt_rand(0, 9999999999), 10, '0', STR_PAD_LEFT); $generatedUsername = "anon-" . $randomDigits;`).
                * Create new record in `anonymous_users` table (`id = AnonymousUserID`, `generated_username`, `first_seen_ip = $clientIp`, `is_active = TRUE`). `last_active_at` will default to `CURRENT_TIMESTAMP`.
                * Issue Anonymous JWT (claims: `sub=AnonymousUserID`, `username=GeneratedRandomUsername`, `type="anonymous_verified"`, `active=true`, `iat`, `exp`=30 days, `lar=iat`). Set HttpOnly cookie.
                * Issue CSRF Token cookie.
                * Update `$userID = AnonymousUserID`, `$userType = "anonymous_verified"`.
        7.  **Else (Valid Active JWT was Present):**
            * Validate CSRF Token from `X-CSRF-Token` header against CSRF cookie. If invalid, return HTTP 403.
            * **Database `is_active` check:** Re-fetch user from `anonymous_users` or `registered_users` using `$userID` and verify their current `is_active` status. If `FALSE`, return HTTP 403 ("User account is deactivated").
        8.  **MySQL User Rate Limit (Message Posts):** Apply 'user_message_post' limit for `$userID` based on `$userType`. If exceeded, return HTTP 429.
        9.  **If all checks pass:**
            * Validate `message_content`, `target_wallet_address`, `wallet_type` (format, length). If invalid, return HTTP 400.
            * Find or create `wallet_id` in `wallets` table for the given `target_wallet_address` and `wallet_type`. If creation fails or wallet invalid, return HTTP 400.
            * Save message to `messages` table: `wallet_id`, `content`, `sender_user_id = $userID`, `is_anonymous_sender = ($userType === "anonymous_verified")`, `sender_ip = $clientIp`.
            * Record successful post in `rate_limit_attempts` for `($userID, 'user_message_post')`.
            * Return HTTP 201 Created (e.g., `{"success": true, "message_id": new_message_id}`).

  * **`POST /api/auth/register`**
      * **Request Body (JSON):** `username`, `email`, `password`, `cf_turnstile_response`.
      * **Logic:**
        1. Get Client IP.
        2. APCu Quick Denylist Check. If IP blocked, HTTP 429.
        3. Validate input fields (presence, format, length, password strength). If invalid, HTTP 400.
        4. If `cf_turnstile_response` missing, HTTP 400.
        5. MySQL IP Rate Limit ('ip_registration_attempt'). If limit hit, add to APCu, HTTP 429.
        6. If IP OK, record attempt.
        7. Verify `cf_turnstile_response` with Cloudflare. If invalid, HTTP 400.
        8. Check if `username` or `email` already exists in `registered_users`. If so, HTTP 409 Conflict.
        9. Hash password using `password_hash()` (BCRYPT algorithm).
        10. Generate `RegisteredUserID` (UUID).
        11. Store new user in `registered_users` (`id=RegisteredUserID`, `username`, `email`, `password_hash`, `is_active=TRUE`, `registration_ip=$clientIp`). `created_at` and `last_active_at` will default.
        12. Issue Registered JWT (claims: `sub=RegisteredUserID`, `username`, `type="registered"`, `active=true`, `iat`, `exp`=6 hours, `lar=iat`). Set HttpOnly cookie.
        13. Issue CSRF Token cookie.
        14. Return HTTP 201 Created (e.g., `{"success": true, "user_id": RegisteredUserID, "username": username}`).

  * **`POST /api/auth/login`**
      * **Request Body (JSON):** `email_or_username`, `password`.
      * **Logic:**
        1. Get Client IP.
        2. APCu Quick Denylist Check. If IP blocked, HTTP 429.
        3. Validate input. If invalid, HTTP 400.
        4. MySQL IP/User Rate Limit (Login Attempts - 'ip_login_attempt' for IP, 'user_login_attempt' for `email_or_username`). If limits hit, add IP to APCu, HTTP 429.
        5. Fetch user from `registered_users` by `email` or `username`.
        6. If user not found OR `user.is_active` is FALSE:
            * Record failed login attempt for IP and `email_or_username` in `rate_limit_attempts`.
            * Return HTTP 401 Unauthorized ("Invalid credentials or account inactive").
        7. Verify `password` against `user.password_hash` using `password_verify()`.
        8. If password mismatch:
            * Record failed login attempt for IP and `email_or_username`.
            * Return HTTP 401 Unauthorized.
        9. If password matches and user is active:
            * Update `last_active_at` in DB: `UPDATE registered_users SET last_active_at = NOW() WHERE id = user.id;`
            * Issue Registered JWT (claims: `sub=user.id`, `username=user.username`, `type="registered"`, `active=true`, `iat`, `exp`=6 hours, `lar=iat`). Set HttpOnly cookie.
            * Issue CSRF Token cookie.
            * (Optional: Clear any specific login attempt counters for this user/IP if you track successes separately to reset limits).
            * Return HTTP 200 OK (e.g., `{"success": true, "user_id": user.id, "username": user.username}`).

  * **`POST /api/auth/logout`**
      * **Logic:**
        1. Clear the `__Host-auth_token` and `__Host-csrf_token` cookies by setting them with an expiry date in the past and an empty value.
        2. Return HTTP 200 OK (`{"success": true}`).

**7. IP Address Handling Details**
  * **True Client IP:** Prioritize `CF-Connecting-IP` (if using Cloudflare and webserver is configured to trust it). Fallback to `X-Forwarded-For` (parse correctly, take the first trusted IP). Final fallback to `$_SERVER['REMOTE_ADDR']`. Ensure this logic is robust against spoofing.

**8. Error Handling & HTTP Status Codes**
  * **Standard JSON Error Response:** `{"success": false, "error": "Descriptive error message for user"}`. In development/debug mode, optionally include `debug_message` and `debug_trace`.
  * **HTTP Status Codes:**
      * `200 OK`: General success.
      * `201 Created`: Resource successfully created.
      * `204 No Content`: Successful action, no content to return (e.g. some successful PUT/DELETE).
      * `400 Bad Request`: Invalid input, missing parameters, invalid CAPTCHA.
      * `401 Unauthorized`: Authentication required or failed (invalid/expired JWT, bad credentials).
      * `403 Forbidden`: Authenticated but not authorized for the action, or CSRF failure, or account inactive.
      * `404 Not Found`: Requested resource does not exist.
      * `409 Conflict`: Resource creation failed due to existing resource (e.g., username/email taken).
      * `429 Too Many Requests`: Rate limit exceeded.
      * `500 Internal Server Error`: Unexpected server-side error (log detailed error for server admins).

**9. Security Considerations Summary**
  * **HTTPS:** Enforce HTTPS sitewide using HSTS headers.
  * **Password Hashing:** Use `password_hash()` with BCRYPT algorithm (default) or Argon2 if available and preferred.
  * **Input Validation:** Server-side validation for all inputs (type, length, format, allowed characters).
  * **XSS Prevention:** Ensure HttpOnly flag on JWT cookie. Properly encode any user-supplied data if ever rendered in HTML (though primarily an API, good practice).
  * **CSRF Protection:** Implement Double Submit Cookie pattern as described.
  * **Dependency Updates:** Regularly update PHP, MySQL, web server, Composer packages (like `ramsey/uuid`), and review Cloudflare Turnstile best practices.
  * **Comprehensive Logging:** Log critical errors, security events (all failed login attempts, rate limit triggers, CAPTCHA failures, CSRF failures), and key actions with timestamps, user IDs (if available), and IPs.
  * **Secure JWT Secret/Keys:** If using HS256, the secret key must be very strong and stored securely, not in client-accessible code. For RS256/ES256, private keys must be secured.

**10. Application Logic for Deactivated (Banned) Senders**
  * Since users are marked `is_active = FALSE` instead of being deleted:
    * When displaying messages and fetching sender details (using `sender_user_id` and `is_anonymous_sender` to query `anonymous_users` or `registered_users`), check the fetched user's `is_active` status.
    * If `is_active` is `FALSE`, the frontend application should display "Sender Deactivated," "User Banned," or a generic "Unknown Sender" instead of their actual username.
    * Message content itself is preserved.
    * Users with `is_active = FALSE` should be prevented from logging in or posting new messages.

**11. Implementation Approach**
  * To be implemented within the existing MVC (Model-View-Controller) architecture of the codebase.
  * **New/Enhanced Models:** `RegisteredUser.php`, `AnonymousUser.php`, `RateLimiter.php` (for MySQL logic), enhance `Message.php`.
  * **New/Enhanced Controllers:** `AuthController.php`, enhance `MessageController.php` (or similar).
  * **New Client-Side JavaScript:** For Turnstile integration, CSRF token handling, and AJAX form submissions for auth and messages.
  * **Database Migrations/Schema Update:** Apply the `schema.sql` changes.
  * **UUID Generation:** Use a library like `ramsey/uuid` for PHP: `composer require ramsey/uuid`.
  * **Anonymous Username Generation:**
    ```php
    // PHP example for generating anonymous username
    $randomDigits = str_pad(mt_rand(0, 9999999999), 10, '0', STR_PAD_LEFT);
    $generatedUsername = "anon-" . $randomDigits;
    ```