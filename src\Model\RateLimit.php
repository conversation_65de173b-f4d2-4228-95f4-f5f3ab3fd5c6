<?php

namespace App\Model;

use App\Core\Database;
use PDO;
use Exception;

/**
 * RateLimit Model
 *
 * Handles operations related to rate limiting
 */
class RateLimit
{
    // Rate limit types
    const TYPE_IP_CAPTCHA_VALIDATION = 'ip_captcha_validation';
    const TYPE_USER_MESSAGE_POST = 'user_message_post';
    const TYPE_USER_TAG_ATTEMPT = 'tag_attempt';
    const TYPE_IP_REGISTRATION_ATTEMPT = 'ip_registration_attempt';
    const TYPE_USER_LOGIN_ATTEMPT = 'user_login_attempt';
    const TYPE_IP_LOGIN_ATTEMPT = 'ip_login_attempt';

    // Rate limit thresholds
    const THRESHOLD_IP_CAPTCHA_VALIDATION = 10; // 10 attempts per 10 minutes
    const THRESHOLD_ANONYMOUS_MESSAGE_POST = 1;  // 1 message per 15 seconds
    const THRESHOLD_REGISTERED_MESSAGE_POST = 20; // 15 messages per minute
    const THRESHOLD_ANONYMOUS_TAG_ATTEMPT = 1; // 1 tag per 15 seconds
    const THRESHOLD_REGISTERED_TAG_ATTEMPT = 5; // 5 tags per minute
    const THRESHOLD_IP_REGISTRATION_ATTEMPT = 2; // 5 attempts per hour
    const THRESHOLD_USER_LOGIN_ATTEMPT = 5; // 5 attempts per 15 minutes
    const THRESHOLD_IP_LOGIN_ATTEMPT = 10; // 10 attempts per 15 minutes

    // APCu denylist TTL (in seconds)
    const APCU_DENYLIST_TTL = 300; // 5 minutes

    /**
     * Record a rate limit attempt
     *
     * @param string $identifier Identifier (IP address or user ID)
     * @param string $identifierType Identifier type (one of the TYPE_* constants)
     * @return bool True if successful
     */
    public static function recordAttempt(string $identifier, string $identifierType): bool
    {
        $db = Database::getInstance();

        $stmt = $db->prepare("
            INSERT INTO rate_limit_attempts
            (identifier, identifier_type)
            VALUES (?, ?)
        ");

        return $stmt->execute([$identifier, $identifierType]);
    }

    /**
     * Check if a rate limit has been exceeded
     *
     * @param string $identifier Identifier (IP address or user ID)
     * @param string $identifierType Identifier type (one of the TYPE_* constants)
     * @param int $threshold Maximum number of attempts allowed
     * @param string $timeWindow Time window for the rate limit (e.g., '10 MINUTE', '1 HOUR')
     * @return bool True if the rate limit has been exceeded
     */
    public static function isExceeded(
        string $identifier,
        string $identifierType,
        int $threshold,
        string $timeWindow
    ): bool {
        // First check APCu denylist if it's an IP-based rate limit
        if (self::isIpBasedRateLimit($identifierType) && self::isIpInDenylist($identifier)) {
            return true;
        }

        $db = Database::getInstance();

        $stmt = $db->prepare("
            SELECT COUNT(*)
            FROM rate_limit_attempts
            WHERE identifier = ?
            AND identifier_type = ?
            AND attempt_timestamp > (NOW() - INTERVAL $timeWindow)
        ");

        $stmt->execute([$identifier, $identifierType]);
        $count = (int)$stmt->fetchColumn();

        return $count >= $threshold;
    }

    /**
     * Check if CAPTCHA validation rate limit is exceeded
     *
     * @param string $ipAddress IP address
     * @return bool True if the rate limit has been exceeded
     */
    public static function isCaptchaValidationLimitExceeded(string $ipAddress): bool
    {
        return self::isExceeded(
            $ipAddress,
            self::TYPE_IP_CAPTCHA_VALIDATION,
            self::THRESHOLD_IP_CAPTCHA_VALIDATION,
            '10 MINUTE'
        );
    }

    /**
     * Check if message posting rate limit is exceeded
     *
     * @param string $userId User ID
     * @param bool $isAnonymous Whether the user is anonymous
     * @return bool True if the rate limit has been exceeded
     */
    public static function isMessagePostLimitExceeded(string $userId, bool $isAnonymous): bool
    {
        if ($isAnonymous) {
            // Anonymous users: 1 message per 15 seconds
            return self::isExceeded(
                $userId,
                self::TYPE_USER_MESSAGE_POST,
                self::THRESHOLD_ANONYMOUS_MESSAGE_POST,
                '15 SECOND'
            );
        } else {
            // Registered users: 15 messages per minute
            return self::isExceeded(
                $userId,
                self::TYPE_USER_MESSAGE_POST,
                self::THRESHOLD_REGISTERED_MESSAGE_POST,
                '1 MINUTE'
            );
        }
    }

    /**
     * Check if tag attempt rate limit is exceeded
     *
     * @param string $userId User ID
     * @param bool $isAnonymous Whether the user is anonymous
     * @return bool True if the rate limit has been exceeded
     */
    public static function isTagAttemptLimitExceeded(string $userId, bool $isAnonymous): bool
    {
        if ($isAnonymous) {
            // Anonymous users: 1 tag per 15 seconds
            return self::isExceeded(
                $userId,
                self::TYPE_USER_TAG_ATTEMPT,
                self::THRESHOLD_ANONYMOUS_TAG_ATTEMPT,
                '15 SECOND'
            );
        } else {
            // Registered users: 5 tags per minute
            return self::isExceeded(
                $userId,
                self::TYPE_USER_TAG_ATTEMPT,
                self::THRESHOLD_REGISTERED_TAG_ATTEMPT,
                '1 MINUTE'
            );
        }
    }

    /**
     * Check if registration attempt rate limit is exceeded
     *
     * @param string $ipAddress IP address
     * @return bool True if the rate limit has been exceeded
     */
    public static function isRegistrationLimitExceeded(string $ipAddress): bool
    {
        return self::isExceeded(
            $ipAddress,
            self::TYPE_IP_REGISTRATION_ATTEMPT,
            self::THRESHOLD_IP_REGISTRATION_ATTEMPT,
            '1 HOUR'
        );
    }

    /**
     * Check if login attempt rate limit is exceeded for a user
     *
     * @param string $usernameOrEmail Username or email
     * @return bool True if the rate limit has been exceeded
     */
    public static function isUserLoginLimitExceeded(string $usernameOrEmail): bool
    {
        return self::isExceeded(
            $usernameOrEmail,
            self::TYPE_USER_LOGIN_ATTEMPT,
            self::THRESHOLD_USER_LOGIN_ATTEMPT,
            '15 MINUTE'
        );
    }

    /**
     * Check if login attempt rate limit is exceeded for an IP address
     *
     * @param string $ipAddress IP address
     * @return bool True if the rate limit has been exceeded
     */
    public static function isIpLoginLimitExceeded(string $ipAddress): bool
    {
        return self::isExceeded(
            $ipAddress,
            self::TYPE_IP_LOGIN_ATTEMPT,
            self::THRESHOLD_IP_LOGIN_ATTEMPT,
            '15 MINUTE'
        );
    }

    /**
     * Add an IP address to the APCu denylist
     *
     * @param string $ipAddress IP address
     * @return bool True if successful
     */
    public static function addIpToDenylist(string $ipAddress): bool
    {
        if (function_exists('apcu_store')) {
            return apcu_store('denylist_ip_' . $ipAddress, 1, self::APCU_DENYLIST_TTL);
        }

        return false;
    }

    /**
     * Check if an IP address is in the APCu denylist
     *
     * @param string $ipAddress IP address
     * @return bool True if the IP is in the denylist
     */
    public static function isIpInDenylist(string $ipAddress): bool
    {
        if (function_exists('apcu_exists')) {
            return apcu_exists('denylist_ip_' . $ipAddress);
        }

        return false;
    }

    /**
     * Check if a rate limit type is IP-based
     *
     * @param string $identifierType Identifier type
     * @return bool True if the rate limit is IP-based
     */
    private static function isIpBasedRateLimit(string $identifierType): bool
    {
        return in_array($identifierType, [
            self::TYPE_IP_CAPTCHA_VALIDATION,
            self::TYPE_IP_REGISTRATION_ATTEMPT,
            self::TYPE_IP_LOGIN_ATTEMPT
        ]);
    }

    /**
     * Clean up old rate limit attempts
     *
     * @param int $days Number of days to keep
     * @return bool True if successful
     */
    public static function cleanupOldAttempts(int $days = 3): bool
    {
        $db = Database::getInstance();

        $stmt = $db->prepare("
            DELETE FROM rate_limit_attempts
            WHERE attempt_timestamp < NOW() - INTERVAL ? DAY
        ");

        return $stmt->execute([$days]);
    }
}
