<?php

namespace App\Controller;

use Exception;

/**
 * Base Controller providing common functionality like view rendering and redirection.
 */
abstract class BaseController
{
    protected ?\App\Model\User $currentUser = null;

    /**
     * Set the current authenticated user.
     * This method is called by the front controller (index.php).
     *
     * @param \App\Model\User|null $user The authenticated user object, or null if not logged in.
     * @return void
     */
    public function setCurrentUser(?\App\Model\User $user): void
    {
        $this->currentUser = $user;
    }

    /**
     * Renders a plain PHP view file, optionally within a layout.
     *
     * Extracts data into variables accessible within the view scope.
     * Handles basic output buffering for layout wrapping.
     * CRITICAL: Assumes manual htmlspecialchars() is used within view files for security.
     *
     * @param string $viewPath Path to the view file relative to the 'views' directory (e.g., 'pages/home').
     * @param array $data Data to extract into variables for the view.
     * @param string|null $layoutPath Path to the layout file relative to 'views' (e.g., 'layouts/main'). If null, view is rendered directly.
     * @return void Outputs the rendered HTML.
     * @throws Exception If view or layout file not found.
     */
    protected function render(string $viewPath, array $data = [], ?string $layoutPath = 'layouts/main'): void
    {
        $viewFile = BASE_PATH . '/views/' . $viewPath . '.php';
        $layoutFile = $layoutPath ? BASE_PATH . '/views/' . $layoutPath . '.php' : null;

        // Add currentUser to the data passed to the view
        $data['currentUser'] = $this->currentUser;

        if (!file_exists($viewFile)) {
            throw new Exception("View file not found: {$viewFile}");
        }

        if ($layoutFile && !file_exists($layoutFile)) {
            throw new Exception("Layout file not found: {$layoutFile}");
        }

        // Extract data variables into the current scope for the view/layout
        extract($data, EXTR_SKIP); // EXTR_SKIP prevents overwriting existing variables like $viewFile

        try {
            ob_start(); // Start output buffering for the main view content

            require $viewFile; // Include the view file, its output is captured

            $content = ob_get_clean(); // Get the captured view content and clean buffer

            if ($layoutFile) {
                // If using a layout, include it. The layout file should echo $content where needed.
                require $layoutFile;
            } else {
                // If no layout, just echo the captured content directly.
                echo $content;
            }
        } catch (\Throwable $e) {
            ob_end_clean(); // Clean buffer on error
            // Re-throw the exception or handle it (e.g., log and show error page)
            // For simplicity here, re-throwing. Consider more robust error handling.
            throw new Exception("Error rendering view '{$viewPath}': " . $e->getMessage(), 0, $e);
        }
    }

     /**
      * Redirects the user to a different URL relative to the application base path.
      *
      * @param string $path The path relative to the application base (e.g., 'search', 'address/btc/123'). Should not start with '/'.
      * @param int $statusCode HTTP status code for redirection (default: 302 Found).
      * @return void
      */
     protected function redirect(string $path, int $statusCode = 302): void
     {
         // Trim leading/trailing slashes from the input path
         $path = trim($path, '/');
         // Construct the full URL using the base path
         $url = $this->getBaseUrl() . $path;

         header('Location: ' . $url, true, $statusCode);
         exit(); // Important to stop script execution after redirect header
     }

    /**
     * Calculates the base URL of the application dynamically.
     * Handles subdirectory deployments.
     *
     * @return string The base URL ending with a slash (e.g., '/' or '/CRYPTAG/').
     */
    protected function getBaseUrl(): string {
        // Use SCRIPT_NAME which points to the front controller (e.g., /CRYPTAG/public/index.php)
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
        // Get the directory containing the script (e.g., /CRYPTAG/public)
        $scriptDir = dirname($scriptName);
        // Get the base path (directory containing 'public'), e.g., /CRYPTAG or /
        $basePath = dirname($scriptDir);

        // Normalize slashes for consistency (Windows)
        $basePath = str_replace('\\', '/', $basePath);

        // Ensure it ends with a slash if it's not the root
        return rtrim($basePath, '/') . '/';
    }

    /**
     * Sends a JSON response.
     *
     * @param array $data The data to encode as JSON.
     * @param int $statusCode HTTP status code (default: 200 OK).
     * @return void
     */
    protected function sendJsonResponse(array $data, int $statusCode = 200): void
    {
        // Clear any previously started output buffering, if any.
        // This helps prevent PHP notices/warnings from breaking JSON if display_errors is on.
        if (ob_get_level() > 0) {
            ob_end_clean();
        }

        header('Content-Type: application/json; charset=utf-8');
        http_response_code($statusCode);
        echo json_encode($data);
        exit(); // Crucial to prevent further script execution and potential output.
    }

    // TODO: Add CSRF token generation/validation methods here later
}
