/*
 * CRYPTAG - Enhanced Styling
 * A dark-themed interface for crypto address profiles and messaging
 */

/* Import Google Fonts - Inter for clean, modern typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Variables for consistent theming */
:root {
    /* Color Palette */
    --color-background: #182136;
    --color-surface: #131a29;
    --color-surface-accent: #1c2438;
    --color-primary: #3b82f6;
    --color-primary-hover: #2563eb;
    --color-secondary: #10b981;
    --color-text-primary: #ffffff;
    --color-text-secondary: #94a3b8;
    --color-text-tertiary: #64748b;
    --color-border: #1e293b;
    --color-error: #ef4444;

    /* Typography */
    --font-family: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm0: 0.35rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;

    /* Transitions */
    --transition-fast: 150ms ease;
    --transition-normal: 250ms ease;
    --transition-slow: 350ms ease;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
    --shadow-md: 0 6px 12px rgba(0, 0, 0, 0.25);
    --shadow-lg: 0 12px 24px rgba(0, 0, 0, 0.3);
}

/* General Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    background-color: var(--color-background);
    background-image:
        radial-gradient(circle at 15% 50%, rgba(59, 130, 246, 0.08) 0%, transparent 25%),
        radial-gradient(circle at 85% 30%, rgba(16, 185, 129, 0.08) 0%, transparent 25%);
    color: var(--color-text-secondary);
    font-family: var(--font-family);
    font-size: 16px;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--color-primary-hover);
}

h1, h2, h3, h4, h5, h6 {
    letter-spacing: normal; /* Adjusted letter-spacing */
    color: var(--color-text-primary);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--spacing-md);
}

h1 .hero-block {
    font-size: 3rem;
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-sm);
}

p {
    margin-bottom: var(--spacing-md);
}

/* Layout Containers */
.container {
    width: 90%;
    max-width: 1340px;
    margin: 0 auto;
    padding: var(--spacing-xl) 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    box-sizing: border-box; /* Ensure padding is included in width calculation */
}

/* Header Styles */
header {
    background-color: rgba(19, 26, 41, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--color-border);
    position: sticky;
    top: 0;
    width: 100%;
    z-index: 100;
}

header .header-container {
    width: 90%;
    max-width: 1340px; /* Match the main container max-width */
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box; /* Ensure padding is included in width calculation */
}

header .logo a {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-text-primary);
    text-decoration: none;
    letter-spacing: -2px;
    display: flex;
    align-items: center;
}

header .logo a:hover {
    color: var(--color-primary);
}

header nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
}

header nav ul li {
    margin-left: var(--spacing-xl);
}

header nav ul li a {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--color-text-secondary);
    letter-spacing: 0.05em;
    position: relative;
    padding: var(--spacing-xs) 0;
}

header nav ul li a:hover {
    color: var(--color-text-primary);
    text-decoration: none;
}

header nav ul li a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--color-primary);
    transition: width var(--transition-normal);
}

header nav ul li a:hover::after {
    width: 100%;
}

/* Footer Styles (Old - to be replaced by .site-footer below) */
/*
footer {
    background-color: var(--color-surface);
    padding: var(--spacing-lg) 0;
    border-top: 1px solid var(--color-border);
    text-align: center;
    color: var(--color-text-tertiary);
    font-size: 0.85rem;
    width: 100%;
    margin-top: auto;
}
*/

/* Homepage Specific Styles */
.hero-block {
    width: 100%;
    text-align: center;
    margin-bottom: var(--spacing-sm);
    cursor: default;
}

.hero-block:hover h1.main-heading {
    text-shadow: 0 0 25px rgba(255, 255, 255, 0.7), 0 0 40px rgba(255, 255, 255, 0.4);
    transform: scale(1.02);
}
h1.main-heading {
    font-size: 4.5rem;
    color: var(--color-text-primary);
    /* margin-top: var(--spacing-lg); Removed top margin */
    margin-bottom: var(--spacing-sm);
    font-weight: 1000;
    letter-spacing: -4px; /* Changed from 0.05em */
    text-align: center;
    width: 100%;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.5), 0 0 30px rgba(255, 255, 255, 0.3);
    animation: glow 2s ease-in-out infinite alternate;
    transition: all 0.3s ease;
}

/* Generic Page Header Styling */
.page-header {
    font-size: 4.5rem;
    margin-bottom: var(--spacing-xl);
    color: var(--color-text-primary);
    text-align: center;
    font-weight: 1000;
    letter-spacing: -4px;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5); /* Add text shadow to lettering */
}

/* Heading with badge container */
.heading-with-badge {
    position: relative;
    display: inline-block;
}

/* ALPHA badge styling */
.alpha-badge {
    position: absolute;
    top: 4.5rem;
    right: 0.2rem; /* Position over the "G" in CRYPTAG */
    left: auto; /* Reset left position */
    background-color: #FFD700; /* Gold/yellow color */
    color: #000000; /* Black text for contrast */
    font-size: 0.7rem;
    font-weight: bold;
    padding: 0.15rem 0.4rem;
    border-radius: 2px;
    transform: rotate(2deg); /* Changed to positive 45deg for right side */
    transform-origin: center;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    z-index: 1;
    letter-spacing: 0.5px; /* Reset letter spacing for the badge */
    text-shadow: none; /* Remove text shadow for better readability */
    animation: none; /* Remove glow animation */
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.2); /* Subtle border for definition */
}

/* Hover effect for the ALPHA badge */
.hero-block:hover .alpha-badge {
    transform: rotate(5deg) scale(1.1);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
}

p.subheading {
    font-size: 1.2rem;
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-xxl);
    max-width: 600px;
    line-height: 1.5;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
}

/* Search Section */
.search-section {
    margin-bottom: var(--spacing-xxl);
    width: 100%;
    max-width: 600px; /* Match the width of the feature boxes */
    margin-left: auto;
    margin-right: auto;
}

.blockchain-selector {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    gap: var(--spacing-sm);
}

.blockchain-selector button {
    background-color: var(--color-surface);
    color: var(--color-text-secondary);
    border: 1px solid var(--color-border);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
    font-size: 0.9rem;
}

.blockchain-selector button.active,
.blockchain-selector button:hover {
    background-color: var(--color-primary);
    color: var(--color-text-primary);
    border-color: var(--color-primary-hover);
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
}

.search-input-group {
    display: flex;
    width: 100%;
    position: relative;
}

.search-input-group input[type="text"] {
    flex-grow: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    margin-bottom: 0;
    font-size: 1rem;
    padding: var(--spacing-md) var(--spacing-lg);
    height: 56px;
}

.search-input-group button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 500;
    letter-spacing: 0.03em;
    height: 56px;
}

/* Form Styles */
form {
    margin-top: var(--spacing-lg);
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Input with Icon Styling */
.input-with-icon {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md); /* Matches form-group margin-bottom */
}

.input-with-icon .input-icon {
    position: absolute;
    left: var(--spacing-md);
    color: var(--color-text-tertiary); /* Icon color */
    pointer-events: none; /* Allow clicks to pass through to input */
    z-index: 1; /* Ensure icon is above input */
}

.input-with-icon input[type="text"],
.input-with-icon input[type="email"],
.input-with-icon input[type="password"] {
    padding-left: calc(var(--spacing-md) + 24px + var(--spacing-sm)); /* Adjust padding for icon */
    width: 100%; /* Ensure input takes full width */
    /* Inherit other styles from general input rules */
}

input[type="text"],
input[type="email"],
input[type="password"],
textarea {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    color: var(--color-text-primary);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-size: 1rem;
    width: 100%;
    margin-bottom: var(--spacing-md);
    box-sizing: border-box;
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
textarea:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}
/* Input validation styles */
input.valid-input {
    border-color: var(--color-secondary);
    background-color: rgba(16, 185, 129, 0.05);
}

input.invalid-input {
    border-color: var(--color-error);
    background-color: rgba(239, 68, 68, 0.05);
}

/* Textarea focus effect */
.focused textarea {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Success highlight animation */
.success-highlight {
    animation: successPulse 1s ease;
}

@keyframes successPulse {
    0% { background-color: var(--color-surface); }
    50% { background-color: rgba(16, 185, 129, 0.2); }
    100% { background-color: var(--color-surface); }
}

@keyframes glow {
    from {
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3), 0 0 20px rgba(255, 255, 255, 0.2);
    }
    to {
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.6), 0 0 30px rgba(255, 255, 255, 0.4), 0 0 40px rgba(255, 255, 255, 0.2);
    }
}

/* Success highlight animation is still used for textarea */

/* No messages placeholder */
.no-messages {
    text-align: center;
    color: var(--color-text-tertiary);
    font-style: italic;
    padding: var(--spacing-lg);
}

/* Loading container */
.loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl) 0;
    color: var(--color-text-secondary);
}

/* Message animation */
.message-item.fade-in {
    animation: fadeInMessage 0.5s ease forwards;
    opacity: 0;
}

@keyframes fadeInMessage {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Ellipsis in pagination */
.pagination .ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 38px;
    color: var(--color-text-tertiary);
}

/* Error container styling */
.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    max-width: 600px;
    margin: 0 auto;
}

.return-link {
    margin-top: var(--spacing-xl);
}

.return-link a {
    display: inline-block;
    background-color: var(--color-surface);
    color: var(--color-text-primary);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-md);
    border: 1px solid var(--color-border);
    transition: all var(--transition-normal);
}

.return-link a:hover {
    background-color: var(--color-surface-accent);
    border-color: var(--color-primary);
    text-decoration: none;
}

textarea {
    min-height: 120px;
    resize: vertical;
    line-height: 1.6;
}

button,
input[type="submit"] {
    background-color: var(--color-primary);
    color: var(--color-text-primary);
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-md);
    font-size: 1rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
}

/* New message styling for seamless auto-refresh */
.message-item.new-message {
    border-left: 3px solid var(--color-secondary);
    background-color: rgba(16, 185, 129, 0.05);
}

button:hover,
input[type="submit"]:hover {
    background-color: var(--color-primary-hover);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    transform: translateY(-1px);
}

button:active,
input[type="submit"]:active {
    transform: translateY(0);
    box-shadow: none;
}

button:disabled,
input[type="submit"]:disabled {
    background-color: var(--color-surface-accent);
    color: var(--color-text-tertiary);
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

/* Generic Card Styling */
.card {
    background-color: var(--color-surface);
    border: 2px solid var(--color-border); /* Thicker border */
    border-radius: var(--radius-md); /* Default to md, specific cards can override */
    box-shadow: var(--shadow-md); /* More noticeable default shadow */
    padding: var(--spacing-lg); /* Default padding, specific cards can override */
    width: 100%; /* Ensure it takes full width of its container */
    box-sizing: border-box;
}

/* Feature Boxes */
.feature-boxes {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xxl);
    width: 100%;
    max-width: 1340px;
    margin-left: auto;
    margin-right: auto;
}

.feature-box {
    /* Inherits from .card */
    padding: var(--spacing-md); /* Override default card padding */
    border-radius: var(--radius-lg); /* Override default card radius */
    flex: 1;
    text-align: center;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    max-width: 300px; /* Set a max width for consistent sizing */
}

.feature-box:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg); /* More pronounced shadow on hover */
}

.feature-box h3 {
    color: var(--color-text-primary);
    font-size: 1.3rem;
    margin-top: 0;
    margin-bottom: var(--spacing-md);
    letter-spacing: 0px;
}

.feature-box p {
    font-size: 0.95rem;
    color: var(--color-text-secondary);
    line-height: 1.6;
}

/* Address Profile Page Styles */
.address-display {
    background-color: var(--color-surface);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-xl);
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 1rem;
    word-wrap: break-word;
    border: 2px solid var(--color-border); /* Thicker border */
    box-shadow: var(--shadow-md); /* Add shadow */
}

/* Removed unused messages-section styles */

/* Removed duplicate message-item styles */

/* Removed duplicate timestamp styles and unused add-message-form styles */

/* Pagination Styles */
.pagination {
    display: flex;
    justify-content: center;
    list-style: none;
    margin: var(--spacing-lg) 0;
    gap: var(--spacing-xs);
}

.pagination li {
    display: inline-block;
}

.pagination button {
    background-color: var(--color-surface);
    color: var(--color-text-secondary);
    border: 1px solid var(--color-border);
    padding: var(--spacing-sm) var(--spacing-md);
    min-width: 40px;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    transition: all var(--transition-fast);
}

.pagination button:hover {
    background-color: var(--color-surface-accent);
    color: var(--color-text-primary);
}

.pagination li.active button {
    background-color: var(--color-primary);
    color: var(--color-text-primary);
    border-color: var(--color-primary);
}

/* Toast notifications have replaced error messages */

/* Loading Indicators */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(59, 130, 246, 0.3);
    border-radius: 50%;
    border-top-color: var(--color-primary);
    animation: spin 1s ease-in-out infinite;
    margin-right: var(--spacing-sm);
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Auto-refresh Indicator */
.auto-refresh-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: var(--color-text-tertiary);
    margin-bottom: var(--spacing-md);
}

.auto-refresh-indicator .indicator {
    width: 8px;
    height: 8px;
    background-color: var(--color-secondary);
    border-radius: 50%;
    margin-right: var(--spacing-xs);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 0.5; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.2); }
    100% { opacity: 0.5; transform: scale(0.8); }
}

/* Authentication Styles */
.auth-card {
    /* Inherits from .card */
    border: 2px solid var(--color-border); /* Thicker border */
    box-shadow: var(--shadow-lg); /* Override default card shadow */
    padding: var(--spacing-xl); /* Override default card padding */
    max-width: 450px;
    margin: 0 auto;
}

.auth-card h1 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    color: var(--color-text-primary);
    font-size: 1.5rem;
    font-weight: 600;
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group input,
.form-group textarea {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--color-text-secondary);
    font-weight: 500;
}

.form-hint {
    font-size: 0.8rem;
    color: var(--color-text-tertiary);
    margin-top: var(--spacing-xs);
}

.auth-footer {
    margin-top: var(--spacing-lg);
    text-align: center;
    color: var(--color-text-tertiary);
}

.terms-blurb {
    font-size: 0.8rem; /* Same size as form-hint */
    color: var(--color-text-tertiary); /* Subtle color */
    margin-top: var(--spacing-sm); /* Small margin at the top */
    line-height: 1.4;
}

/* Cloudflare Turnstile styling */
.cf-turnstile {
    margin: var(--spacing-md) 0;
    display: flex;
    justify-content: center;
}

/* Error messages now use toast notifications */

.btn-full {
    width: 100%;
}

.btn-primary {
    background-color: var(--color-primary);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.btn-primary:hover {
    background-color: var(--color-primary-hover);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    transform: translateY(-1px);
}

/* Checkbox Styles */
.checkbox-container {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    position: relative;
    padding-left: 30px;
    margin-bottom: var(--spacing-sm);
    user-select: none;
    line-height: 1.4;
}

.checkbox-container input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
    margin: 0;
}

.checkmark {
    position: absolute;
    top: 2px;
    left: 0;
    height: 18px;
    width: 18px;
    background-color: var(--color-surface);
    border: 2px solid var(--color-border);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.checkbox-container:hover input ~ .checkmark {
    border-color: var(--color-primary);
    background-color: var(--color-surface-accent);
}

.checkbox-container input:checked ~ .checkmark {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-container .checkmark:after {
    left: 5px;
    top: 1px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-text {
    color: var(--color-text-secondary);
    font-size: 0.95rem;
    margin-left: var(--spacing-xs);
}

.checkbox-text strong {
    color: var(--color-primary);
    font-weight: 600;
}

.anonymous-link-section {
    background-color: var(--color-surface-accent);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

/* Responsive Styles */
@media (max-width: 768px) {
    h1.main-heading {
        font-size: 2.5rem;
    }

    p.subheading {
        font-size: 1rem;
    }

    .feature-boxes {
        flex-direction: column;
    }

    .feature-box {
        width: 100%;
        margin-bottom: var(--spacing-lg);
    }

    .search-input-group {
        flex-direction: column;
    }

    .search-input-group input[type="text"] {
        border-radius: var(--radius-md);
        margin-bottom: var(--spacing-sm);
    }

    .search-input-group button {
        border-radius: var(--radius-md);
        width: 100%;
    }

    header .logo a {
        font-size: 1.2rem;
    }

    header nav ul li {
        margin-left: var(--spacing-lg);
    }

    /* Adjust ALPHA badge position for smaller screens */
    .alpha-badge {
        top: 0.1rem;
        right: 0.6rem;
        left: auto;
        font-size: 0.6rem;
        padding: 0.1rem 0.3rem;
    }
}

@media (max-width: 480px) {
    .container,
    header .header-container,
    .footer-main-content {
        width: 95%;
        padding: var(--spacing-lg) 0;
    }

    h1.main-heading {
        font-size: 2rem;
    }

    header .header-container {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: 0; /* Remove padding for header container */
    }

    header nav ul {
        justify-content: center;
    }

    header nav ul li {
        margin: 0 var(--spacing-md);
    }

    /* Further adjust ALPHA badge for very small screens */
    .alpha-badge {
        top: 0.1rem;
        right: 0.5rem;
        left: auto;
        font-size: 0.5rem;
        padding: 0.1rem 0.25rem;
    }
}

/* Wallet Profile Page Redesign */
#wallet-profile-container {
    width: 900px;
    max-width: 90%;
    margin: 0 auto;
    box-sizing: border-box;
    text-align: left;
}

.profile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 2px solid var(--color-border); /* Thicker border */
    box-shadow: var(--shadow-md); /* Add shadow */
}

.profile-header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.wallet-icon {
    width: 48px;
    height: 48px;
    background-color: var(--color-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    font-weight: bold;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.wallet-icon [data-lucide] {
    width: 1.5em;
    height: 1.5em;
    stroke-width: 2;
    color: white;
}

.wallet-type-icon {
    letter-spacing: -0.5px;
}

.wallet-info h2 {
    margin: 0;
    font-size: 1.4rem;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    letter-spacing: 0.5px;
}

.wallet-network {
    color: var(--color-text-secondary);
    margin: var(--spacing-xs) 0 0 0;
    font-size: 0.9rem;
}

.profile-header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.action-button {
    background-color: var(--color-surface-accent);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.action-button:hover {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.action-button:active {
    transform: translateY(0);
    box-shadow: none;
}

.button-icon {
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Lucide icon styling */
[data-lucide] {
    stroke-width: 2;
    vertical-align: middle;
}

.button-icon [data-lucide] {
    width: 1em;
    height: 1em;
    margin-right: 0.25rem;
}

/* Icon animation for button feedback */
@keyframes icon-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.icon-pulse {
    animation: icon-pulse 0.5s ease-in-out;
}









/* Enhanced Footer Styles - Consolidated */
.site-footer {
    background-color: var(--color-surface);
    color: var(--color-text-secondary);
    padding: var(--spacing-xl) 0; /* Match container padding */
    border-top: 1px solid var(--color-border);
    font-size: 0.9rem;
    margin-top: auto;
    width: 100%; /* Full width background */
    display: flex;
    justify-content: center; /* Center the footer content */
}

.footer-main-content { /* Renamed from footer-container */
    width: 90%; /* Match the main container width */
    max-width: 1340px; /* Match the main container max-width */
    margin: 0 auto; /* Centered */
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    text-align: left;
    box-sizing: border-box; /* Ensure padding is included in width calculation */
    padding: 0; /* Remove any padding that might affect alignment */
}

.footer-column {
    box-sizing: border-box; /* Ensure padding is included in width calculation */
}

.footer-column h4 {
    color: var(--color-text-primary);
    font-size: 1.1rem;
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.footer-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-column ul li {
    margin-bottom: var(--spacing-sm0);
}

.footer-column ul li a {
    color: var(--color-text-secondary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-column ul li a:hover {
    color: var(--color-primary);
    text-decoration: underline;
}

.footer-column-left .footer-site-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-text-primary);
}

.footer-column-left .footer-site-info [data-lucide] {
    width: 28px;
    height: 28px;
    color: var(--color-primary);
}

.footer-column-left .site-description {
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--color-text-tertiary);
    max-width: 300px;
    margin-bottom: var(--spacing-md); /* Added margin below description */
}

.footer-socials-inline { /* New class for inline socials */
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-md); /* Space before copyright */
}

.footer-socials-inline a {
    color: var(--color-text-secondary);
    transition: color var(--transition-fast), transform var(--transition-fast);
}

.footer-socials-inline a:hover {
    color: var(--color-primary);
    transform: scale(1.1);
}

.footer-socials-inline [data-lucide] {
    width: 24px;
    height: 24px;
}

.footer-copyright-inline p { /* New class for inline copyright */
    font-size: 0.85rem;
    color: var(--color-text-tertiary);
    margin: 0; /* Remove default p margin */
}


/* Dropdown Container for User Profile Icon */
.dropdown-container {
    position: relative;
    display: inline-block; /* Allows positioning relative to text flow */
}

/* User Profile Icon */
.user-profile-icon {
    width: 36px; /* Match avatar size */
    height: 36px; /* Match avatar size */
    background-color: var(--color-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.user-profile-icon:hover {
    background-color: var(--color-primary-hover);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.user-profile-icon [data-lucide] {
    width: 1.2em; /* Adjust icon size */
    height: 1.2em; /* Adjust icon size */
    stroke-width: 2;
}

/* Dropdown Menu */
.dropdown-menu {
    position: absolute;
    top: calc(100% + var(--spacing-sm)); /* Position below the icon with some spacing */
    right: 0; /* Align to the right of the icon */
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    /* list-style: none; */ /* No longer a list */
    padding: var(--spacing-sm) 0; /* Vertical padding; items control horizontal */
    /* width: max-content; */ /* Removed */
    /* min-width: 150px; */ /* Removed */
    z-index: 1000; /* Ensure it's above other content */
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: opacity var(--transition-fast), transform var(--transition-fast), visibility var(--transition-fast);
    display: block; /* Ensure it's a block element to stack items vertically */
    overflow: hidden; /* Clip overflowing content, especially hover background */
    text-align: left; /* Ensure text is left-aligned within the menu */
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}



/* New Dropdown Item Styles for div > a > p structure */
.dropdown-item { /* This is the <a> tag */
    display: block;
    padding: var(--spacing-sm) var(--spacing-md); /* Vertical sm, Horizontal sm - add padding back */
    color: var(--color-text-secondary);
    text-decoration: none;
    transition: background-color var(--transition-fast), color var(--transition-fast);
}

.dropdown-item:hover {
    background-color: transparent; /* Remove background highlight for whole box */
    color: var(--color-text-primary);
    /* text-decoration: none !important; */ /* Moved to .dropdown-menu a for broader effect */
}

/* Ensure no underline for any link within the dropdown menu */
.dropdown-menu a {
    text-decoration: none !important;
}

.dropdown-item p {
    margin: 0;
    display: flex;
    align-items: center;
}
/* END New Dropdown Item Styles */


.dropdown-icon {
    width: 1em; /* Tiny icon size */
    height: 1em; /* Tiny icon size */
    margin-right: var(--spacing-sm); /* Space between icon and text */
    margin-left: 0; /* Ensure no left margin */
    padding-left: 0; /* Ensure no left padding */
    display: inline-block; /* Ensure predictable spacing behavior */
    vertical-align: middle; /* Align icon with text if 'a' is block */
    stroke-width: 2;
    color: var(--color-text-tertiary); /* Subtle icon color */
}

/* Responsive adjustments for footer */
@media (max-width: 768px) {
    .footer-main-content {
        grid-template-columns: 1fr; /* Stack columns on smaller screens */
        text-align: center; /* Center text for stacked columns */
    }

    .footer-column-left .site-description,
    .footer-column-left .footer-copyright-inline p {
        margin-left: auto;
        margin-right: auto;
        max-width: 100%; /* Allow full width when centered */
    }

    .footer-column-left .footer-site-info,
    .footer-column-left .footer-socials-inline {
        justify-content: center; /* Center site info and socials when stacked */
    }

    .footer-column h4 {
        margin-top: var(--spacing-lg); /* Add some space above headings when stacked */
    }
    .footer-column:first-child h4 { /* This might not be needed if only left col has h4 */
        margin-top: 0;
    }
}


