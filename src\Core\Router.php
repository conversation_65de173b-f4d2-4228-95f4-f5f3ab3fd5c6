<?php

namespace App\Core;

use Exception; // Or a custom NotFoundException

/**
 * Simple Router class.
 *
 * Matches request URIs to controller actions based on defined routes.
 */
class Router
{
    /**
     * Stores registered routes.
     * Structure: ['GET' => [['uri' => '/path', 'action' => [Controller::class, 'method']]], 'POST' => [...]]
     * @var array<string, array<int, array{uri: string, action: array{0: class-string, 1: string}}>>
     */
    protected array $routes = [];

    /**
     * Add a route to the routing table.
     *
     * @param string $method HTTP method (GET, POST, etc.)
     * @param string $uri URI pattern (e.g., '/users/{id}')
     * @param array{0: class-string, 1: string} $action Controller class and method name array
     * @return void
     */
    public function add(string $method, string $uri, array $action): void
    {
        $method = strtoupper($method);
        // Basic validation for action format
        if (count($action) !== 2 || !is_string($action[0]) || !is_string($action[1])) {
             throw new \InvalidArgumentException("Invalid action format for route {$method} {$uri}. Expected [Controller::class, 'methodName'].");
        }
        $this->routes[$method][] = ['uri' => $this->normalizeUri($uri), 'action' => $action];
    }

    /**
     * Normalize URI by ensuring it starts with a slash and optionally removing trailing slash.
     *
     * @param string $uri
     * @return string
     */
    private function normalizeUri(string $uri): string
    {
        $uri = trim($uri, '/');
        return '/' . $uri;
    }

    /**
     * Dispatch the request to the appropriate route.
     *
     * @param Request $request The request object
     * @return array{action: array{0: class-string, 1: string}, params: array<string, string>} Matched route details
     * @throws Exception If no route matches (consider a specific NotFoundException)
     */
    public function dispatch(Request $request): array
    {
        $requestMethod = $request->getMethod();
        $requestUri = $request->getUri();

        if (!isset($this->routes[$requestMethod])) {
            throw new Exception("No routes defined for method {$requestMethod}.", 404); // Or custom exception
        }

        foreach ($this->routes[$requestMethod] as $route) {
            $pattern = $this->compileRoutePattern($route['uri']);
            
            if (preg_match($pattern, $requestUri, $matches)) {
                // Extract named parameters
                $params = [];
                foreach ($matches as $key => $value) {
                    if (is_string($key)) { // Only keep named capture groups
                        $params[$key] = $value;
                    }
                }

                return [
                    'action' => $route['action'],
                    'params' => $params
                ];
            }
        }

        throw new Exception("No route found for {$requestMethod} {$requestUri}", 404); // Or custom exception
    }

    /**
     * Compiles a route URI pattern into a regex pattern.
     * Converts '/users/{id}' into '#^/users/(?<id>[^/]+)$#'.
     *
     * @param string $uri Route URI pattern
     * @return string Regex pattern
     */
    private function compileRoutePattern(string $uri): string
    {
        // Escape forward slashes and replace placeholders like {id} with named capture groups
        $pattern = preg_replace('/\{([a-zA-Z0-9_]+)\}/', '(?<$1>[^/]+)', $uri);
        // Add start/end anchors and make it case-insensitive if needed (optional)
        return '#^' . $pattern . '$#'; // Using # as delimiter
    }

    // Convenience methods for common HTTP verbs
    public function get(string $uri, array $action): void { $this->add('GET', $uri, $action); }
    public function post(string $uri, array $action): void { $this->add('POST', $uri, $action); }
    public function put(string $uri, array $action): void { $this->add('PUT', $uri, $action); }
    public function delete(string $uri, array $action): void { $this->add('DELETE', $uri, $action); }
    public function patch(string $uri, array $action): void { $this->add('PATCH', $uri, $action); }

}