<?php

namespace App\Core;

/**
 * Validates cryptocurrency address formats according to specified rules.
 * This validator checks for structural correctness (prefix, character set, length).
 * For Bitcoin, full Base58Check/Bech32 checksums are not verified by this class.
 * For Ethereum, EIP-55 checksum is not cryptographically verified by this class.
 * For Solana, it checks if the address decodes to a 32-byte public key.
 */
class AddressValidator
{
    // Bitcoin and Solana Base58 Character Set
    private const BASE58_CHARS = '**********************************************************';
    // Bech32 allowed data characters regex (for Bitcoin)
    private const BECH32_DATA_CHARS_REGEX = '/^[qpzry9x8gf2tvdw0s3jn54khce6mua7l02-9]+$/';

    /**
     * Validates an address based on its type.
     *
     * @param string $type The type of cryptocurrency (e.g., 'btc', 'eth', 'sol'). Case-insensitive.
     * @param string $address The address string to validate.
     * @return bool True if the address format is considered valid for the type, false otherwise.
     */
    public function validate(string $type, string $address): bool
    {
        $type = strtolower(trim($type));
        $address = trim($address);

        if (empty($type) || empty($address)) {
            return false;
        }

        switch ($type) {
            case 'btc':
                return $this->validateBtc($address);
            case 'eth':
                return $this->validateEth($address);
            case 'sol':
                return $this->validateSol($address);
            default:
                // Unknown type is considered invalid
                return false;
        }
    }

    /**
     * Validates Bitcoin address format.
     * Covers P2PKH, P2SH, and Bech32 (P2WPKH, P2WSH, P2TR) formats.
     * Checks prefix, character set, and length.
     * WARNING: Does not perform full Base58Check or Bech32 checksum validation. Use libraries for that.
     *
     * @param string $address The Bitcoin address to validate.
     * @return bool True if the address structure is valid, false otherwise.
     */
    private function validateBtc(string $address): bool
    {
        // P2PKH: Starts with '1', Base58, 26-35 characters.
        if (strpos($address, '1') === 0) {
            $len = strlen($address);
            if ($len >= 26 && $len <= 35) {
                // Check for valid Base58 characters
                for ($i = 0; $i < $len; $i++) {
                    if (strpos(self::BASE58_CHARS, $address[$i]) === false) {
                        return false;
                    }
                }
                // NOTE: Full validation requires Base58Check checksum verification.
                return true;
            }
        }
        // P2SH: Starts with '3', Base58, typically 34 characters.
        elseif (strpos($address, '3') === 0) {
            if (strlen($address) === 34) {
                 // Check for valid Base58 characters
                for ($i = 0; $i < strlen($address); $i++) {
                    if (strpos(self::BASE58_CHARS, $address[$i]) === false) {
                        return false;
                    }
                }
                // NOTE: Full validation requires Base58Check checksum verification.
                return true;
            }
        }
        // Bech32 (SegWit - P2WPKH, P2WSH, P2TR): Starts with 'bc1' (mainnet) or 'tb1' (testnet).
        elseif (strpos($address, 'bc1') === 0 || strpos($address, 'tb1') === 0) {
            $addrLower = strtolower($address); // Bech32 is case-insensitive for processing.
            $len = strlen($addrLower);

            // Extract the part after 'bc1' or 'tb1' for character validation
            $hrpCandidate = (strpos($addrLower, 'bc1') === 0) ? 'bc1' : 'tb1';
            $dataCharsPart = substr($addrLower, strlen($hrpCandidate));


            if (empty($dataCharsPart) || !preg_match(self::BECH32_DATA_CHARS_REGEX, $dataCharsPart)) {
                 return false;
            }

            // Validate based on specific prefixes and lengths:
            // P2WPKH (e.g., bc1q...): 42 characters.
            // P2WSH (e.g., bc1p...): 62 characters. (Also Taproot P2TR)
            $prefix4Chars = substr($addrLower, 0, 4); // e.g., "bc1q", "tb1p"

            if (($prefix4Chars === $hrpCandidate.'q') && $len === 42) {
                // NOTE: Full validation requires Bech32 checksum verification.
                return true;
            } elseif (($prefix4Chars === $hrpCandidate.'p') && $len === 62) { // Bech32m for P2TR uses 'p'
                // NOTE: Full validation requires Bech32 checksum verification.
                return true;
            }
        }
        return false;
    }

    /**
     * Validates Ethereum (and EVM-compatible) address format.
     * Checks for '0x' prefix, 40 hexadecimal characters, and exact length.
     * WARNING: Does not cryptographically verify EIP-55 checksum for mixed-case addresses. Use libraries for that.
     *
     * @param string $address The Ethereum address to validate.
     * @return bool True if the address structure is valid, false otherwise.
     */
    private function validateEth(string $address): bool
    {
        if (strpos($address, '0x') !== 0) {
            return false;
        }
        $hexPart = substr($address, 2);
        if (strlen($hexPart) !== 40) {
            return false;
        }
        if (!ctype_xdigit($hexPart)) { // ctype_xdigit checks for [0-9a-fA-F]
            return false;
        }

        // Regarding EIP-55 checksum:
        // This basic validator checks structure. If an address is mixed-case,
        // full EIP-55 validation would require keccak256 hashing.
        // An all-lowercase or all-uppercase address is structurally valid without EIP-55.
        // if ($this->hasMixedCase($hexPart)) {
        //     // A library should be used for actual EIP-55 validation.
        // }
        return true;
    }

    /**
     * Validates Solana address format.
     * Checks if the string is valid Base58 and decodes to exactly 32 bytes.
     * WARNING: Does not verify that the decoded public key is a valid point on the Ed25519 curve.
     * Requires GMP or BCMath PHP extension for Base58 decoding.
     *
     * @param string $address The Solana address to validate.
     * @return bool True if the address structure is valid, false otherwise.
     */
    private function validateSol(string $address): bool
    {
        // Preliminary length check (typical user wallets are 43-44 chars, program IDs can be shorter)
        $len = strlen($address);
        if ($len < 32 || $len > 44) {
            // This is a loose check; the main validation is the decoded byte length.
            // For example, "1" is valid Base58 but will fail the 32-byte decode check.
        }

        if (!extension_loaded('gmp') && !extension_loaded('bcmath')) {
            error_log("AddressValidator (Solana): GMP or BCMath extension is required for Base58 decoding.");
            return false; // Cannot perform validation without necessary extensions.
        }

        $decoded_bytes = $this->_base58_decode($address);

        if ($decoded_bytes === null) {
            // Decoding failed (e.g., invalid characters in address string)
            return false;
        }

        // A Solana public key must be exactly 32 bytes long.
        return strlen($decoded_bytes) === 32;
    }

    /**
     * Decodes a Base58 encoded string. Used for Solana address validation.
     * Requires GMP or BCMath extension.
     *
     * @param string $base58_string The Base58 encoded string.
     * @return string|null The decoded binary string, or null if input is invalid or extensions are missing.
     */
    private function _base58_decode(string $base58_string): ?string
    {
        $alphabet = self::BASE58_CHARS;
        $base = strlen($alphabet);

        if (extension_loaded('gmp')) {
            $num = gmp_init(0);
        } elseif (extension_loaded('bcmath')) {
            $num = '0';
        } else {
            // This case should be caught by validateSol before calling _base58_decode
            return null;
        }

        for ($i = 0; $i < strlen($base58_string); $i++) {
            $char = $base58_string[$i];
            $char_value = strpos($alphabet, $char);

            if ($char_value === false) {
                return null; // Invalid character
            }

            if (extension_loaded('gmp')) {
                $num = gmp_mul($num, $base);
                $num = gmp_add($num, $char_value);
            } elseif (extension_loaded('bcmath')) {
                $num = bcmul($num, (string)$base);
                $num = bcadd($num, (string)$char_value);
            }
        }

        $bytes = '';
        if (extension_loaded('gmp')) {
            if (gmp_cmp($num, 0) == 0) {
                $hex = '00'; // Ensure at least one byte for value 0
            } else {
                $hex = gmp_strval($num, 16);
            }
            if (strlen($hex) % 2 !== 0) {
                $hex = '0' . $hex;
            }
            if (!empty($hex)) {
                $bytes_candidate = @hex2bin($hex); // Suppress errors from invalid hex
                if ($bytes_candidate !== false) {
                    $bytes = $bytes_candidate;
                } else {
                    return null; // hex2bin failed
                }
            } elseif (gmp_cmp($num, 0) == 0 && strlen($base58_string) > 0 && $base58_string[0] === '1') {
                 // Special case for input "1" which means a single 0 byte if not handled by leading '1's logic
                 // This part of logic for GMP needs to be careful with how gmp_strval(0) and hex2bin handle it.
                 // If $num is 0, $hex becomes "00", $bytes becomes chr(0).
            }


        } elseif (extension_loaded('bcmath')) {
            if (bccomp($num, '0') === 0) {
                 // If the number is 0, the result is a single zero byte,
                 // unless leading '1's dictate more.
                 // This will be prepended correctly by the leading '1's loop if $base58_string was "1".
                 // If $base58_string was empty or invalid, it would have returned null.
                 // If $base58_string led to $num = 0 (e.g. "1"), this branch is hit.
                 $bytes = chr(0);
            } else {
                $temp_bytes = '';
                while (bccomp($num, '0') > 0) {
                    $remainder = bcmod($num, '256');
                    $temp_bytes = chr((int)$remainder) . $temp_bytes;
                    $num = bcdiv($num, '256', 0);
                }
                $bytes = $temp_bytes;
            }
        }

        // Prepend leading zero bytes (represented by '1' in Base58)
        // Important: This loop must run *after* the main conversion,
        // and it should only add a chr(0) for each leading '1' *if* the character '1'
        // itself decodes to 0.
        // The current loop correctly adds chr(0) for each leading '1'.
        // However, if the initial $bytes was chr(0) from $num being 0 (e.g. input "1"),
        // we need to be careful not to double-add.
        $leading_zeros = 0;
        for ($i = 0; $i < strlen($base58_string) && $base58_string[$i] === '1'; $i++) {
            $leading_zeros++;
        }

        if (extension_loaded('gmp')) {
            // If $num was initially 0 (e.g. input "1"), $bytes is already chr(0).
            // We need $leading_zeros chr(0) in total.
            if (gmp_strval(gmp_init(0),16) === gmp_strval(gmp_init($num),16) && $leading_zeros > 0) {
                 // This condition needs to be precise. If $num (after loop) is 0.
                 // If original $base58_string was "1", $num becomes 0. hex2bin("00") -> chr(0).
                 // leading_zeros is 1. We need one chr(0).
                 // If original was "11", $num becomes 0. bytes is chr(0). leading_zeros is 2. We need two chr(0).
                 // So, $bytes should be str_repeat(chr(0), $leading_zeros) if $num ended up as 0.
                 if(gmp_cmp(gmp_init(0), $num) == 0) { // If the numeric value is zero
                    $bytes = str_repeat(chr(0), $leading_zeros);
                 } else { // If numeric value is non-zero, prepend $leading_zeros
                    $bytes = str_repeat(chr(0), $leading_zeros) . $bytes;
                 }

            } else { // If $num was non-zero, just prepend.
                 $bytes = str_repeat(chr(0), $leading_zeros) . $bytes;
            }
             // Correction: If the number itself is 0 (e.g., input "1"), gmp_strval($num, 16) is "0".
             // hex2bin("00") results in a single \0 byte.
             // The leading zero loop then adds one \0. This is correct for "1".
             // For "11", $num is 0. $bytes is \0. leading zero loop adds \0\0. This is correct.
             // The logic for GMP to build $bytes from $hex should be fine.
             // The leading zero part:
             // If $num is 0, $bytes from hex is `chr(0)`. We need $leading_zeros total.
             // So if $leading_zeros > 0, $bytes becomes `str_repeat(chr(0), $leading_zeros)`.
             // If $num > 0, $bytes is non-empty. We prepend `str_repeat(chr(0), $leading_zeros)`.

            $original_num_for_gmp = gmp_init(0); // Need to capture initial $num for gmp to compare
             for ($k_gmp = 0; $k_gmp < strlen($base58_string); $k_gmp++) {
                $char_gmp = $base58_string[$k_gmp];
                $char_value_gmp = strpos($alphabet, $char_gmp);
                if ($char_value_gmp === false) break; // Should have been caught
                $original_num_for_gmp = gmp_mul($original_num_for_gmp, $base);
                $original_num_for_gmp = gmp_add($original_num_for_gmp, $char_value_gmp);
            }

            if (gmp_cmp($original_num_for_gmp, 0) == 0 && $leading_zeros > 0) {
                $bytes = str_repeat(chr(0), $leading_zeros);
            } else if ($leading_zeros > 0) {
                 // If $bytes was chr(0) because $original_num_for_gmp was 0, but there were no leading '1's,
                 // this would be wrong. The current $bytes is from $original_num_for_gmp.
                 // Prepend $leading_zeros to the $bytes derived from $original_num_for_gmp.
                 $current_bytes_from_num = '';
                 if (gmp_cmp($original_num_for_gmp, 0) == 0) { $hex_val = '00'; }
                 else { $hex_val = gmp_strval($original_num_for_gmp, 16); }
                 if (strlen($hex_val) % 2 !== 0) { $hex_val = '0' . $hex_val; }
                 if(!empty($hex_val)) { $current_bytes_from_num = @hex2bin($hex_val) ?: '';}

                $bytes = str_repeat(chr(0), $leading_zeros) . $current_bytes_from_num;
            }
            // If $leading_zeros is 0, $bytes is already correct from $original_num_for_gmp.

        } elseif (extension_loaded('bcmath')) {
            // For bcmath, $bytes is built by repeated division.
            // If $num was 0 (e.g. input "1"), $bytes is already chr(0).
            // We need $leading_zeros chr(0) in total.
            if (bccomp($num, '0') === 0 && $leading_zeros > 0 && $bytes === chr(0)) {
                 $bytes = str_repeat(chr(0), $leading_zeros);
            } else { // If $num > 0 or ($num == 0 and $leading_zeros == 0)
                 $bytes = str_repeat(chr(0), $leading_zeros) . $bytes;
            }
        }
        // Final check: if the input was ONLY '1's, $num would be 0.
        // $bytes from $num might be a single chr(0).
        // The leading zero loop should correctly construct all chr(0)s.
        // Example: input "11" -> $num=0. $bytes from $num could be chr(0).
        // $leading_zeros = 2. str_repeat(chr(0),2) . chr(0) -> WRONG.
        // It should just be str_repeat(chr(0), $leading_zeros) if $num is 0.

        // Re-simplifying leading zero logic:
        // 1. Convert the numeric value of base58_string (ignoring leading '1's for a moment) to bytes.
        // 2. Separately, count leading '1's. Each leading '1' is a zero byte.
        // 3. Concatenate: zero bytes from leading '1's + bytes from numeric value.
        //    Exception: if the entire string is '1's, the numeric value is 0.
        //               The bytes from numeric value might be empty or a single chr(0).
        //               The result should be $leading_zeros of chr(0).

        $value_part_of_string = ltrim($base58_string, '1');
        $num_val = extension_loaded('gmp') ? gmp_init(0) : '0';
        for ($k = 0; $k < strlen($value_part_of_string); $k++) {
            $char_k = $value_part_of_string[$k];
            $char_val_k = strpos($alphabet, $char_k);
            if ($char_val_k === false) return null; // Should have been caught

            if (extension_loaded('gmp')) {
                $num_val = gmp_mul($num_val, $base);
                $num_val = gmp_add($num_val, $char_val_k);
            } else {
                $num_val = bcmul($num_val, (string)$base);
                $num_val = bcadd($num_val, (string)$char_val_k);
            }
        }

        $value_bytes = '';
        if (extension_loaded('gmp')) {
            if (gmp_cmp($num_val, 0) == 0 && !empty($value_part_of_string)) { // e.g. "11A", value_part "A"
                 $hex_v = gmp_strval($num_val, 16); // if num_val is 0, this is "0"
                 if (strlen($hex_v) % 2 !== 0) $hex_v = '0' . $hex_v; else if(empty($hex_v)) $hex_v = '00';
                 if ($hex_v == '0') $hex_v = '00'; // ensure it's "00" for gmp_strval("0")
                 $value_bytes = @hex2bin($hex_v) ?: '';
            } else if (gmp_cmp($num_val, 0) > 0) {
                $hex_v = gmp_strval($num_val, 16);
                if (strlen($hex_v) % 2 !== 0) $hex_v = '0' . $hex_v;
                $value_bytes = @hex2bin($hex_v) ?: '';
            }
            // If $value_part_of_string is empty (all '1's), $num_val is 0, $value_bytes is ''.
        } elseif (extension_loaded('bcmath')) {
            if (bccomp($num_val, '0') === 0 && !empty($value_part_of_string)) {
                 $value_bytes = chr(0); // If value part is not empty but decodes to 0.
            } else if (bccomp($num_val, '0') > 0) {
                $temp_vb = '';
                while (bccomp($num_val, '0') > 0) {
                    $rem_vb = bcmod($num_val, '256');
                    $temp_vb = chr((int)$rem_vb) . $temp_vb;
                    $num_val = bcdiv($num_val, '256', 0);
                }
                $value_bytes = $temp_vb;
            }
             // If $value_part_of_string is empty (all '1's), $num_val is 0, $value_bytes is ''.
        }

        $num_leading_ones = strlen($base58_string) - strlen($value_part_of_string);
        $leading_one_bytes = str_repeat(chr(0), $num_leading_ones);

        // If the original string was ONLY '1's (e.g. "1", "11"),
        // $value_part_of_string is empty, $value_bytes is empty. Result is $leading_one_bytes. Correct.
        // If original string was "1A", $leading_one_bytes is chr(0). $value_bytes is from "A". Correct.
        // If original string was "A", $leading_one_bytes is empty. $value_bytes is from "A". Correct.
        return $leading_one_bytes . $value_bytes;
    }


    /**
     * Checks if a string (expected to be the hex part of an ETH address) contains mixed case hexadecimal characters.
     * @param string $hexString The hexadecimal string (without '0x' prefix).
     * @return bool True if mixed case (contains both [a-f] and [A-F]), false otherwise.
     */
    private function hasMixedCase(string $hexString): bool
    {
        if (empty($hexString)) {
            return false;
        }
        $hasLower = preg_match('/[a-f]/', $hexString);
        $hasUpper = preg_match('/[A-F]/', $hexString);
        return $hasLower && $hasUpper;
    }
}