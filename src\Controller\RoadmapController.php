<?php

namespace App\Controller;

// use App\Core\Request; // No longer strictly needed if not used in method signature

/**
 * RoadmapController
 *
 * Handles displaying the product roadmap page.
 */
class RoadmapController extends BaseController
{
    /**
     * Displays the roadmap page.
     *
     * @return void
     */
    public function index(): void // Removed Request $request parameter
    {
        // Data for the view (could be expanded if needed)
        $data = [
            'pageTitle' => 'Cryptag Product Roadmap',
            'pageDescription' => 'Our one-year public product roadmap for Cryptag, detailing upcoming features and plans from May 2025 to May 2026.',
            // 'canonicalUrl' will be constructed in the view using $this->getBaseUrl()
        ];

        // The render method in BaseController will handle including the main layout.
        // It expects the view path relative to the 'views' directory.
        $this->render('pages/roadmap', $data);
    }
}