<?php

namespace App\Model;

use App\Core\Database;
use App\Helpers\UuidHelper;
use PDO;
use Exception;

/**
 * Unified User Model
 *
 * Handles operations related to both registered and anonymous users.
 */
class User
{
    // Common properties
    private string $id;
    private string $type; // 'registered' or 'anonymous_verified'
    private bool $is_active;
    private string $created_at;
    private ?string $last_active_at;

    // Registered user specific properties
    private ?string $username = null;
    private ?string $email = null;
    private ?string $password_hash = null;
    private ?bool $is_admin = null;
    private ?string $registration_ip = null;
    private ?string $updated_at = null;

    // Anonymous user specific properties
    private ?string $generated_username = null;
    private ?string $first_seen_ip = null;
    private ?string $claimed_by_registered_user_id = null;

    /**
     * Private constructor to enforce static factory methods.
     *
     * @param array $data User data from database.
     */
    private function __construct(array $data)
    {
        $this->id = $data['id'];
        $this->is_active = (bool)($data['is_active'] ?? true);
        $this->created_at = $data['created_at'];
        $this->last_active_at = $data['last_active_at'] ?? null;

        // Determine user type and assign specific properties
        if (isset($data['username']) && isset($data['email']) && isset($data['password_hash'])) {
            $this->type = 'registered';
            $this->username = $data['username'];
            $this->email = $data['email'];
            $this->password_hash = $data['password_hash'];
            $this->is_admin = (bool)($data['is_admin'] ?? false);
            $this->registration_ip = $data['registration_ip'] ?? null;
            $this->updated_at = $data['updated_at'] ?? null;
        } elseif (isset($data['generated_username']) && isset($data['first_seen_ip'])) {
            $this->type = 'anonymous_verified';
            $this->generated_username = $data['generated_username'];
            $this->first_seen_ip = $data['first_seen_ip'];
            $this->claimed_by_registered_user_id = $data['claimed_by_registered_user_id'] ?? null;
        } else {
            throw new Exception("Invalid user data provided for User model instantiation.");
        }
    }

    /**
     * Factory method to create a User object from database data.
     *
     * @param array $userData
     * @return User
     */
    private static function createFromArray(array $userData): User
    {
        return new self($userData);
    }

    /**
     * Create a new registered user.
     *
     * @param string $username Username
     * @param string $email Email address
     * @param string $password_hash Hashed password
     * @param string $registration_ip Registration IP address
     * @param bool $is_active Whether the user is active
     * @param bool $is_admin Whether the user is an admin
     * @return User
     */
    public static function createRegisteredUser(
        string $username,
        string $email,
        string $password_hash,
        string $registration_ip,
        bool $is_active = true,
        bool $is_admin = false
    ): User {
        $db = Database::getInstance();
        $id = UuidHelper::generateUuid();

        $stmt = $db->prepare("
            INSERT INTO registered_users
            (id, username, email, password_hash, is_active, is_admin, registration_ip)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $id,
            $username,
            $email,
            $password_hash,
            $is_active ? 1 : 0,
            $is_admin ? 1 : 0,
            $registration_ip
        ]);

        return self::getById($id, 'registered');
    }

    /**
     * Create a new anonymous user.
     *
     * @param string $first_seen_ip IP address where the user was first seen
     * @param bool $is_active Whether the user is active
     * @return User
     */
    public static function createAnonymousUser(string $first_seen_ip, bool $is_active = true): User
    {
        $db = Database::getInstance();
        $id = UuidHelper::generateUuid();
        $generated_username = UuidHelper::generateAnonymousUsername();

        $stmt = $db->prepare("
            INSERT INTO anonymous_users
            (id, generated_username, first_seen_ip, is_active)
            VALUES (?, ?, ?, ?)
        ");

        $stmt->execute([
            $id,
            $generated_username,
            $first_seen_ip,
            $is_active ? 1 : 0
        ]);

        return self::getById($id, 'anonymous_verified');
    }

    /**
     * Get a user by ID and type.
     *
     * @param string $id User ID
     * @param string $type 'registered' or 'anonymous_verified'
     * @return User|null User object or null if not found
     */
    public static function getById(string $id, string $type): ?User
    {
        $db = Database::getInstance();
        $tableName = ($type === 'registered') ? 'registered_users' : 'anonymous_users';

        $stmt = $db->prepare("SELECT * FROM {$tableName} WHERE id = ?");
        $stmt->execute([$id]);
        $userData = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$userData) {
            return null;
        }

        return self::createFromArray($userData);
    }

    /**
     * Get a user by username (for registered users) or generated username (for anonymous users).
     *
     * @param string $username Username or generated username
     * @param string $type 'registered' or 'anonymous_verified'
     * @return User|null User object or null if not found
     */
    public static function getByUsername(string $username, string $type): ?User
    {
        $db = Database::getInstance();
        $tableName = ($type === 'registered') ? 'registered_users' : 'anonymous_users';
        $columnName = ($type === 'registered') ? 'username' : 'generated_username';

        $stmt = $db->prepare("SELECT * FROM {$tableName} WHERE {$columnName} = ?");
        $stmt->execute([$username]);
        $userData = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$userData) {
            return null;
        }

        return self::createFromArray($userData);
    }

    /**
     * Get a user by email or username (for registered users) or generated username (for anonymous users).
     * This method attempts to find a user across both tables if necessary.
     *
     * @param string $identifier Email, username, or generated username
     * @return User|null User object or null if not found
     */
    public static function getByUsernameOrEmail(string $identifier): ?User
    {
        $db = Database::getInstance();

        // Try to find in registered_users first (by email or username)
        $stmt = $db->prepare("SELECT * FROM registered_users WHERE username = ? OR email = ?");
        $stmt->execute([$identifier, $identifier]);
        $userData = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($userData) {
            return self::createFromArray($userData);
        }

        // If not found in registered_users, try to find in anonymous_users by generated_username
        $stmt = $db->prepare("SELECT * FROM anonymous_users WHERE generated_username = ?");
        $stmt->execute([$identifier]);
        $userData = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($userData) {
            return self::createFromArray($userData);
        }

        return null;
    }

    /**
     * Check if a username already exists in registered users.
     *
     * @param string $username Username to check
     * @return bool True if the username exists
     */
    public static function usernameExists(string $username): bool
    {
        $db = Database::getInstance();
        $stmt = $db->prepare("SELECT COUNT(*) FROM registered_users WHERE username = ?");
        $stmt->execute([$username]);
        return (int)$stmt->fetchColumn() > 0;
    }

    /**
     * Validate username format for registration.
     * Only allows: 0-9, a-Z, and . (period)
     * Length: 3-30 characters
     *
     * @param string $username Username to validate
     * @return array Array with 'valid' boolean and 'error' message if invalid
     */
    public static function validateUsername(string $username): array
    {
        // Check length
        if (strlen($username) < 3) {
            return ['valid' => false, 'error' => 'Username must be at least 3 characters long'];
        }

        if (strlen($username) > 30) {
            return ['valid' => false, 'error' => 'Username must be no more than 30 characters long'];
        }

        // Check allowed characters: 0-9, a-Z, and . (period)
        if (!preg_match('/^[a-zA-Z0-9.]+$/', $username)) {
            return ['valid' => false, 'error' => 'Username can only contain letters (a-Z), numbers (0-9), and periods (.)'];
        }

        // Check if username starts or ends with a period
        if (str_starts_with($username, '.') || str_ends_with($username, '.')) {
            return ['valid' => false, 'error' => 'Username cannot start or end with a period'];
        }

        // Check for consecutive periods
        if (str_contains($username, '..')) {
            return ['valid' => false, 'error' => 'Username cannot contain consecutive periods'];
        }

        return ['valid' => true, 'error' => ''];
    }

    /**
     * Check if an email already exists in registered users.
     *
     * @param string $email Email to check
     * @return bool True if the email exists
     */
    public static function emailExists(string $email): bool
    {
        $db = Database::getInstance();
        $stmt = $db->prepare("SELECT COUNT(*) FROM registered_users WHERE email = ?");
        $stmt->execute([$email]);
        return (int)$stmt->fetchColumn() > 0;
    }

    /**
     * Update the last_active_at timestamp for a user.
     *
     * @param string $id User ID
     * @param string $type 'registered' or 'anonymous_verified'
     * @return bool True if successful
     */
    public static function updateLastActiveAt(string $id, string $type): bool
    {
        $db = Database::getInstance();
        $tableName = ($type === 'registered') ? 'registered_users' : 'anonymous_users';
        $stmt = $db->prepare("UPDATE {$tableName} SET last_active_at = NOW() WHERE id = ?");
        return $stmt->execute([$id]);
    }

    /**
     * Claim an anonymous user by a registered user.
     *
     * @param string $anonymousUserId Anonymous user ID
     * @param string $registeredUserId Registered user ID
     * @return bool True if successful
     */
    public static function claimAnonymousUser(string $anonymousUserId, string $registeredUserId): bool
    {
        $db = Database::getInstance();
        $stmt = $db->prepare("
            UPDATE anonymous_users
            SET claimed_by_registered_user_id = ?
            WHERE id = ? AND claimed_by_registered_user_id IS NULL
        ");
        return $stmt->execute([$registeredUserId, $anonymousUserId]);
    }

    // Getters for common properties
    public function getId(): string
    {
        return $this->id;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function isActive(): bool
    {
        return $this->is_active;
    }

    public function getCreatedAt(): string
    {
        return $this->created_at;
    }

    public function getLastActiveAt(): ?string
    {
        return $this->last_active_at;
    }

    // Getters for registered user specific properties
    public function getUsername(): ?string
    {
        return $this->username;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function getPasswordHash(): ?string
    {
        return $this->password_hash;
    }

    public function isAdmin(): ?bool
    {
        return $this->is_admin;
    }

    public function getRegistrationIp(): ?string
    {
        return $this->registration_ip;
    }

    public function getUpdatedAt(): ?string
    {
        return $this->updated_at;
    }

    // Getters for anonymous user specific properties
    public function getGeneratedUsername(): ?string
    {
        return $this->generated_username;
    }

    public function getFirstSeenIp(): ?string
    {
        return $this->first_seen_ip;
    }

    public function getClaimedByRegisteredUserId(): ?string
    {
        return $this->claimed_by_registered_user_id;
    }

    public function isClaimed(): bool
    {
        return $this->claimed_by_registered_user_id !== null;
    }
}
