/**
 * Tags CSS - Styling for wallet tagging feature
 * Badge-style tags with outline/filled states and notification counters
 */

/* Tag But<PERSON> in Profile Header - matches action-button styling */
.tag-button {
    background-color: var(--color-surface-accent);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.tag-button:hover {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.tag-button:active {
    transform: translateY(0);
    box-shadow: none;
}

.tag-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    background-color: var(--color-surface-accent);
    color: var(--color-text-tertiary);
}

.tag-button [data-lucide] {
    width: 1em;
    height: 1em;
    stroke-width: 2;
}

/* Tag Dropdown Container */
.tag-dropdown {
    position: relative;
    display: inline-block;
}

.tag-dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--color-surface);
    border: 2px solid var(--color-border);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-md);
    min-width: 280px;
    max-width: 320px;
    z-index: 1000;
    margin-top: var(--spacing-xs);
    display: none;
}

.tag-dropdown-content.show {
    display: block;
}

.tag-dropdown-header {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--color-border);
}

.tag-dropdown-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-text-primary);
    margin: 0;
}

.tag-dropdown-subtitle {
    font-size: 0.85rem;
    color: var(--color-text-secondary);
    margin: var(--spacing-xs) 0 0 0;
}

/* Available Tags Grid */
.available-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    max-height: 200px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--color-primary) var(--color-surface-accent);
}

.available-tags::-webkit-scrollbar {
    width: 6px;
}

.available-tags::-webkit-scrollbar-track {
    background: var(--color-surface-accent);
    border-radius: 3px;
}

.available-tags::-webkit-scrollbar-thumb {
    background-color: var(--color-primary);
    border-radius: 3px;
}

/* Tag Badge Styles */
.tag-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    border: 1px solid var(--color-primary);
    background-color: transparent;
    color: var(--color-primary);
    user-select: none;
}

/* Outline state (default) */
.tag-badge.outline {
    background-color: transparent;
    color: var(--color-primary);
    border-color: var(--color-primary);
}

.tag-badge.outline:hover {
    background-color: var(--color-primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

/* Filled state (user has applied this tag) */
.tag-badge.filled {
    background-color: var(--color-primary);
    color: white;
    border-color: var(--color-primary);
    cursor: not-allowed;
    opacity: 0.7;
}

.tag-badge.filled:hover {
    transform: none;
    box-shadow: none;
}

/* Disabled state */
.tag-badge:disabled,
.tag-badge.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    pointer-events: none;
}

/* Popular Tags Section */
.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.popular-tag {
    position: relative;
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    background-color: var(--color-primary);
    color: white;
    border: 1px solid var(--color-primary);
}

/* Notification-style counter */
.tag-counter {
    position: absolute;
    top: -6px;
    right: -6px;
    background-color: #ef4444; /* Red for notifications */
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
    border: 2px solid var(--color-surface);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Hide counter if count is 1 */
.tag-counter.single {
    display: none;
}

/* Confirmation Popover */
.tag-confirmation-popover {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--color-surface);
    border: 2px solid var(--color-border);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-lg);
    max-width: 400px;
    z-index: 2000;
    display: none;
}

.tag-confirmation-popover.show {
    display: block;
}

.tag-confirmation-header {
    margin-bottom: var(--spacing-md);
}

.tag-confirmation-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--color-text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.tag-confirmation-message {
    color: var(--color-text-secondary);
    margin: 0;
    line-height: 1.4;
}

.tag-confirmation-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    background-color: var(--color-primary);
    color: white;
    margin: var(--spacing-sm) 0;
}

.tag-confirmation-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

.tag-confirm-btn,
.tag-cancel-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    border: none;
}

.tag-confirm-btn {
    background-color: var(--color-primary);
    color: white;
}

.tag-confirm-btn:hover {
    background-color: #2563eb;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.tag-cancel-btn {
    background-color: transparent;
    color: var(--color-text-secondary);
    border: 1px solid var(--color-border);
}

.tag-cancel-btn:hover {
    background-color: var(--color-surface-accent);
    color: var(--color-text-primary);
}

/* Overlay for popover */
.tag-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1500;
    display: none;
}

.tag-overlay.show {
    display: block;
}

/* Loading states */
.tag-loading {
    opacity: 0.6;
    pointer-events: none;
}

.tag-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid var(--color-border);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}


/* Empty state */
.tag-placeholder {
    color: var(--color-text-tertiary);
    font-style: italic;
    font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tag-dropdown-content {
        right: auto;
        left: 0;
        min-width: 260px;
        max-width: 300px;
    }

    .tag-confirmation-popover {
        max-width: 90vw;
        margin: 0 var(--spacing-md);
    }
}
