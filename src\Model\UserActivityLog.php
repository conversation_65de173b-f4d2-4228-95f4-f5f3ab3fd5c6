<?php

namespace App\Model;

use App\Core\Database;
use PDO;
use PDOException;
use App\Helpers\IpHelper;

/**
 * UserActivityLog Model
 *
 * Handles logging and retrieval of user activity across the application.
 * Supports both registered and anonymous users with detailed action tracking.
 */
class UserActivityLog
{
    private PDO $db;

    // Authentication Actions
    const ACTION_LOGIN_SUCCESS = 'login_success';
    const ACTION_LOGIN_FAIL = 'login_fail';
    const ACTION_LOGOUT = 'logout';
    const ACTION_REGISTER_SUCCESS = 'register_success';
    const ACTION_PASSWORD_CHANGE = 'password_change';

    // Content Actions
    const ACTION_MESSAGE_CREATED = 'message_created';
    const ACTION_ITEM_TAGGED = 'item_tagged';
    const ACTION_ITEM_LIKED = 'item_liked';
    const ACTION_WALLET_SEARCHED = 'wallet_searched';
    const ACTION_PROFILE_VIEWED = 'profile_viewed';

    // Security Actions
    const ACTION_SUSPICIOUS_ACTIVITY = 'suspicious_activity';
    const ACTION_ACCOUNT_DEACTIVATED = 'account_deactivated';
    const ACTION_CAPTCHA_FAILED = 'captcha_failed';
    const ACTION_RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded';

    public function __construct()
    {
        $this->db = Database::getConnection();
    }

    /**
     * Log a user activity with optional details
     *
     * @param string $userId UUID of the user
     * @param bool $isAnonymous Whether the user is anonymous
     * @param string $actionType Type of action performed
     * @param array|null $actionDetails Optional details about the action
     * @param string|null $ipAddress IP address (auto-detected if null)
     * @param string|null $userAgent User agent (auto-detected if null)
     * @return bool Success status
     */
    public function logActivity(
        string $userId,
        bool $isAnonymous,
        string $actionType,
        ?array $actionDetails = null,
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): bool {
        try {
            // Auto-detect IP and user agent if not provided
            $ipAddress = $ipAddress ?? IpHelper::getClientIp();
            $userAgent = $userAgent ?? ($_SERVER['HTTP_USER_AGENT'] ?? null);

            // Convert action details to JSON
            $detailsJson = $actionDetails ? json_encode($actionDetails) : null;

            $sql = "INSERT INTO user_activity_log
                    (user_id, is_anonymous_user, action_type, action_details, ip_address, user_agent, created_at)
                    VALUES (:user_id, :is_anonymous, :action_type, :action_details, :ip_address, :user_agent, NOW())";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_STR);
            $stmt->bindParam(':is_anonymous', $isAnonymous, PDO::PARAM_BOOL);
            $stmt->bindParam(':action_type', $actionType, PDO::PARAM_STR);
            $stmt->bindParam(':action_details', $detailsJson, PDO::PARAM_STR);
            $stmt->bindParam(':ip_address', $ipAddress, PDO::PARAM_STR);
            $stmt->bindParam(':user_agent', $userAgent, PDO::PARAM_STR);

            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error logging user activity: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get recent activity for a specific user
     *
     * @param string $userId UUID of the user
     * @param bool $isAnonymous Whether the user is anonymous
     * @param int $limit Number of records to return
     * @param int $offset Offset for pagination
     * @return array Array of activity records
     */
    public function getUserActivity(string $userId, bool $isAnonymous, int $limit = 50, int $offset = 0): array
    {
        try {
            $sql = "SELECT id, action_type, action_details, ip_address, created_at
                    FROM user_activity_log
                    WHERE user_id = :user_id AND is_anonymous_user = :is_anonymous
                    ORDER BY created_at DESC
                    LIMIT :limit OFFSET :offset";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_STR);
            $stmt->bindParam(':is_anonymous', $isAnonymous, PDO::PARAM_BOOL);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();

            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Decode JSON action details
            foreach ($results as &$record) {
                $record['action_details'] = $record['action_details'] ?
                    json_decode($record['action_details'], true) : null;
            }

            return $results;
        } catch (PDOException $e) {
            error_log("Error getting user activity: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get activity by action type
     *
     * @param string $actionType Type of action to filter by
     * @param int $limit Number of records to return
     * @param int $hours Time range in hours (default 24)
     * @return array Array of activity records
     */
    public function getActivityByType(string $actionType, int $limit = 100, int $hours = 24): array
    {
        try {
            $sql = "SELECT id, user_id, is_anonymous_user, action_details, ip_address, created_at
                    FROM user_activity_log
                    WHERE action_type = :action_type
                    AND created_at >= DATE_SUB(NOW(), INTERVAL :hours HOUR)
                    ORDER BY created_at DESC
                    LIMIT :limit";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':action_type', $actionType, PDO::PARAM_STR);
            $stmt->bindParam(':hours', $hours, PDO::PARAM_INT);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();

            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Decode JSON action details
            foreach ($results as &$record) {
                $record['action_details'] = $record['action_details'] ?
                    json_decode($record['action_details'], true) : null;
            }

            return $results;
        } catch (PDOException $e) {
            error_log("Error getting activity by type: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get recent activity across all users
     *
     * @param int $hours Time range in hours
     * @param int $limit Number of records to return
     * @return array Array of activity records
     */
    public function getRecentActivity(int $hours = 24, int $limit = 100): array
    {
        try {
            $sql = "SELECT id, user_id, is_anonymous_user, action_type, action_details, ip_address, created_at
                    FROM user_activity_log
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL :hours HOUR)
                    ORDER BY created_at DESC
                    LIMIT :limit";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':hours', $hours, PDO::PARAM_INT);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();

            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Decode JSON action details
            foreach ($results as &$record) {
                $record['action_details'] = $record['action_details'] ?
                    json_decode($record['action_details'], true) : null;
            }

            return $results;
        } catch (PDOException $e) {
            error_log("Error getting recent activity: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get security-related events for a user
     *
     * @param string $userId UUID of the user
     * @param bool $isAnonymous Whether the user is anonymous
     * @param int $days Number of days to look back
     * @return array Array of security events
     */
    public function getSecurityEvents(string $userId, bool $isAnonymous, int $days = 30): array
    {
        try {
            $securityActions = [
                self::ACTION_LOGIN_FAIL,
                self::ACTION_SUSPICIOUS_ACTIVITY,
                self::ACTION_CAPTCHA_FAILED,
                self::ACTION_RATE_LIMIT_EXCEEDED,
                self::ACTION_ACCOUNT_DEACTIVATED
            ];

            $placeholders = str_repeat('?,', count($securityActions) - 1) . '?';

            $sql = "SELECT id, action_type, action_details, ip_address, created_at
                    FROM user_activity_log
                    WHERE user_id = ? AND is_anonymous_user = ?
                    AND action_type IN ($placeholders)
                    AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                    ORDER BY created_at DESC";

            $stmt = $this->db->prepare($sql);
            $params = [$userId, $isAnonymous, ...$securityActions, $days];
            $stmt->execute($params);

            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Decode JSON action details
            foreach ($results as &$record) {
                $record['action_details'] = $record['action_details'] ?
                    json_decode($record['action_details'], true) : null;
            }

            return $results;
        } catch (PDOException $e) {
            error_log("Error getting security events: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get activity statistics for a time period
     *
     * @param string $period Time period ('1h', '24h', '7d', '30d')
     * @return array Statistics array
     */
    public function getActivityStats(string $period = '24h'): array
    {
        try {
            // Convert period to MySQL interval
            $intervalMap = [
                '1h' => '1 HOUR',
                '24h' => '1 DAY',
                '7d' => '7 DAY',
                '30d' => '30 DAY'
            ];

            $interval = $intervalMap[$period] ?? '1 DAY';

            $sql = "SELECT
                        COUNT(*) as total_actions,
                        COUNT(DISTINCT user_id) as unique_users,
                        COUNT(DISTINCT CASE WHEN is_anonymous_user = 1 THEN user_id END) as anonymous_users,
                        COUNT(DISTINCT CASE WHEN is_anonymous_user = 0 THEN user_id END) as registered_users,
                        COUNT(DISTINCT ip_address) as unique_ips
                    FROM user_activity_log
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL $interval)";

            $stmt = $this->db->prepare($sql);
            $stmt->execute();

            return $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
        } catch (PDOException $e) {
            error_log("Error getting activity stats: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get top actions by frequency for a time period
     *
     * @param string $period Time period ('1h', '24h', '7d', '30d')
     * @param int $limit Number of top actions to return
     * @return array Array of action types with counts
     */
    public function getTopActions(string $period = '24h', int $limit = 10): array
    {
        try {
            $intervalMap = [
                '1h' => '1 HOUR',
                '24h' => '1 DAY',
                '7d' => '7 DAY',
                '30d' => '30 DAY'
            ];

            $interval = $intervalMap[$period] ?? '1 DAY';

            $sql = "SELECT action_type, COUNT(*) as count
                    FROM user_activity_log
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL $interval)
                    GROUP BY action_type
                    ORDER BY count DESC
                    LIMIT :limit";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting top actions: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get user statistics
     *
     * @param string $userId UUID of the user
     * @param bool $isAnonymous Whether the user is anonymous
     * @return array User statistics
     */
    public function getUserStats(string $userId, bool $isAnonymous): array
    {
        try {
            $sql = "SELECT
                        COUNT(*) as total_actions,
                        COUNT(DISTINCT action_type) as unique_action_types,
                        MIN(created_at) as first_activity,
                        MAX(created_at) as last_activity,
                        COUNT(DISTINCT DATE(created_at)) as active_days
                    FROM user_activity_log
                    WHERE user_id = :user_id AND is_anonymous_user = :is_anonymous";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_STR);
            $stmt->bindParam(':is_anonymous', $isAnonymous, PDO::PARAM_BOOL);
            $stmt->execute();

            return $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
        } catch (PDOException $e) {
            error_log("Error getting user stats: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Clean up old activity logs
     *
     * @param int $daysToKeep Number of days to keep (default 90)
     * @return int Number of records deleted
     */
    public function cleanupOldLogs(int $daysToKeep = 90): int
    {
        try {
            $sql = "DELETE FROM user_activity_log
                    WHERE created_at < DATE_SUB(NOW(), INTERVAL :days DAY)";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':days', $daysToKeep, PDO::PARAM_INT);
            $stmt->execute();

            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Error cleaning up old logs: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Export user activity data (for privacy compliance)
     *
     * @param string $userId UUID of the user
     * @param bool $isAnonymous Whether the user is anonymous
     * @return array Complete user activity data
     */
    public function exportUserData(string $userId, bool $isAnonymous): array
    {
        try {
            $sql = "SELECT action_type, action_details, ip_address, user_agent, created_at
                    FROM user_activity_log
                    WHERE user_id = :user_id AND is_anonymous_user = :is_anonymous
                    ORDER BY created_at ASC";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_STR);
            $stmt->bindParam(':is_anonymous', $isAnonymous, PDO::PARAM_BOOL);
            $stmt->execute();

            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Decode JSON action details
            foreach ($results as &$record) {
                $record['action_details'] = $record['action_details'] ?
                    json_decode($record['action_details'], true) : null;
            }

            return $results;
        } catch (PDOException $e) {
            error_log("Error exporting user data: " . $e->getMessage());
            return [];
        }
    }

    // Static helper methods for common logging scenarios

    /**
     * Log a login attempt (success or failure)
     *
     * @param object $user User object with getId() and getType() methods
     * @param bool $success Whether login was successful
     * @param array $details Additional details (username, method, etc.)
     * @return bool Success status
     */
    public static function logLogin($user, bool $success = true, array $details = []): bool
    {
        $instance = new self();
        $actionType = $success ? self::ACTION_LOGIN_SUCCESS : self::ACTION_LOGIN_FAIL;
        $isAnonymous = $user->getType() === 'anonymous_verified';

        return $instance->logActivity($user->getId(), $isAnonymous, $actionType, $details);
    }

    /**
     * Log a message creation
     *
     * @param object $user User object
     * @param int $walletId Wallet ID where message was posted
     * @param int $messageId Created message ID
     * @param array $details Additional details
     * @return bool Success status
     */
    public static function logMessage($user, int $walletId, int $messageId, array $details = []): bool
    {
        $instance = new self();
        $isAnonymous = $user->getType() === 'anonymous_verified';

        $details = array_merge($details, [
            'wallet_id' => $walletId,
            'message_id' => $messageId
        ]);

        return $instance->logActivity($user->getId(), $isAnonymous, self::ACTION_MESSAGE_CREATED, $details);
    }

    /**
     * Log a tag application
     *
     * @param object $user User object
     * @param int $walletId Wallet ID that was tagged
     * @param int $tagId Tag ID that was applied
     * @param array $details Additional details
     * @return bool Success status
     */
    public static function logTag($user, int $walletId, int $tagId, array $details = []): bool
    {
        $instance = new self();
        $isAnonymous = $user->getType() === 'anonymous_verified';

        $details = array_merge($details, [
            'wallet_id' => $walletId,
            'tag_id' => $tagId
        ]);

        return $instance->logActivity($user->getId(), $isAnonymous, self::ACTION_ITEM_TAGGED, $details);
    }

    /**
     * Log a wallet search
     *
     * @param object $user User object
     * @param string $query Search query
     * @param bool $resultsFound Whether results were found
     * @param array $details Additional details
     * @return bool Success status
     */
    public static function logSearch($user, string $query, bool $resultsFound, array $details = []): bool
    {
        $instance = new self();
        $isAnonymous = $user->getType() === 'anonymous_verified';

        $details = array_merge($details, [
            'search_query' => $query,
            'results_found' => $resultsFound
        ]);

        return $instance->logActivity($user->getId(), $isAnonymous, self::ACTION_WALLET_SEARCHED, $details);
    }

    /**
     * Log a security event
     *
     * @param object $user User object
     * @param string $reason Reason for security event
     * @param array $details Additional details
     * @return bool Success status
     */
    public static function logSecurity($user, string $reason, array $details = []): bool
    {
        $instance = new self();
        $isAnonymous = $user->getType() === 'anonymous_verified';

        $details = array_merge($details, [
            'reason' => $reason
        ]);

        return $instance->logActivity($user->getId(), $isAnonymous, self::ACTION_SUSPICIOUS_ACTIVITY, $details);
    }

    /**
     * Log a profile view
     *
     * @param object $user User object
     * @param int $walletId Wallet ID that was viewed
     * @param array $details Additional details
     * @return bool Success status
     */
    public static function logProfileView($user, int $walletId, array $details = []): bool
    {
        $instance = new self();
        $isAnonymous = $user->getType() === 'anonymous_verified';

        $details = array_merge($details, [
            'wallet_id' => $walletId
        ]);

        return $instance->logActivity($user->getId(), $isAnonymous, self::ACTION_PROFILE_VIEWED, $details);
    }
}
