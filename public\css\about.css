/* About Page Styles */
.about-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
}

/* .about-title removed - now using .page-header from style.css */

.about-section {
    /* Inherits from .card */
    margin-bottom: var(--spacing-xxl); /* Keep specific margin */
    padding: var(--spacing-lg); /* Keep specific padding */
}

.about-section h2 {
    font-size: 1.8rem;
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--color-border);
    text-align: left;
    font-weight: 650;
    letter-spacing: -2px;
}

.about-section p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
    color: var(--color-text-secondary);
    text-align: left;
}

.about-section ul {
    list-style-type: none;
    padding: 0;
    margin-bottom: var(--spacing-md);
    text-align: left;
}

.about-section li {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-sm);
    color: var(--color-text-secondary);
    position: relative;
    padding-left: var(--spacing-lg);
    display: flex;
    align-items: flex-start;
}

.about-section li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--color-primary);
    font-size: 1.2rem;
    line-height: 1.5;
}

.about-section a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.about-section a:hover {
    color: var(--color-primary-hover);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    /* .about-title responsive rule removed - now handled by .page-header in style.css */

    .about-section h2 {
        font-size: 1.5rem;
    }

    .about-section p,
    .about-section li {
        font-size: 1rem;
    }
}
