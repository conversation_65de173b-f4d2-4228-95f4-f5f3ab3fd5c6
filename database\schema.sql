-- Schema for the Cryptag application
-- Timestamp: 2025-05-19 (Updated for last_active_at defaults)

-- Wallets Table: Stores unique wallet addresses and their types
CREATE TABLE IF NOT EXISTS `wallets` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `type` VARCHAR(10) NOT NULL COMMENT 'e.g., btc, eth, sol',
  `address` VARCHAR(255) NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `idx_type_address` (`type`, `address`) -- Ensure unique combination
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Registered Users Table: Stores user accounts that have explicitly registered
CREATE TABLE IF NOT EXISTS `registered_users` (
  `id` CHAR(36) PRIMARY KEY COMMENT 'UUID generated by <PERSON><PERSON> application',
  `username` VARCHA<PERSON>(50) NOT NULL UNIQUE,
  `email` VARCHAR(255) NOT NULL UNIQUE,
  `password_hash` VARCHAR(255) NOT NULL COMMENT 'Hash generated by password_hash()',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'TRUE if user is active/not banned, FALSE otherwise',
  `is_admin` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'TRUE if user has admin privileges, FALSE otherwise',
  `registration_ip` VARCHAR(45) NULL COMMENT 'IP address used during registration',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_active_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp of the last known user activity, updated by application logic'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Anonymous Users Table: Stores records for users who post anonymously (after CAPTCHA)
CREATE TABLE IF NOT EXISTS `anonymous_users` (
  `id` CHAR(36) PRIMARY KEY COMMENT 'UUID generated by PHP application',
  `generated_username` VARCHAR(50) NOT NULL COMMENT 'Format: anon- followed by 10 random digits',
  `first_seen_ip` VARCHAR(45) NULL,
  `claimed_by_registered_user_id` CHAR(36) NULL,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'TRUE if user is active/not banned, FALSE otherwise',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `last_active_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp of the last known user activity, updated by application logic',
  FOREIGN KEY (`claimed_by_registered_user_id`) REFERENCES `registered_users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Messages Table: Stores messages (tags) associated with wallets
CREATE TABLE IF NOT EXISTS `messages` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `wallet_id` INT NOT NULL COMMENT 'FK to wallets.id - the target wallet/profile for the message',
  `content` TEXT NOT NULL,
  `sender_user_id` CHAR(36) NOT NULL COMMENT 'UUID of the sender, from anonymous_users or registered_users table',
  `is_anonymous_sender` BOOLEAN NOT NULL COMMENT 'TRUE if sender_user_id refers to anonymous_users, FALSE if registered_users',
  `sender_ip` VARCHAR(45) NULL COMMENT 'IP Address of the sender',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`wallet_id`) REFERENCES `wallets`(`id`) ON DELETE CASCADE -- Link to wallets table
  -- Note: No direct FK from sender_user_id here due to polymorphic nature. Application must ensure integrity.
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Rate Limit Attempts Table: Tracks attempts for various actions to enforce rate limits
CREATE TABLE IF NOT EXISTS `rate_limit_attempts` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `identifier` VARCHAR(255) NOT NULL COMMENT 'Stores IP address or UserID (CHAR(36) UUID)',
  `identifier_type` ENUM('ip_captcha_validation', 'user_message_post', 'ip_registration_attempt', 'user_login_attempt') NOT NULL,
  `attempt_timestamp` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_rate_limit_check` (`identifier`, `identifier_type`, `attempt_timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;