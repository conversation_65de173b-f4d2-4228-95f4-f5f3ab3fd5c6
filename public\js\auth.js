/**
 * Authentication utilities for CRYPTAG
 */
const Auth = {
    /**
     * Get the CSRF token from the cookie
     *
     * @returns {string|null} CSRF token or null if not found
     */
    getCsrfToken: function() {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith('csrf_token=')) {
                return cookie.substring('csrf_token='.length, cookie.length);
            }
        }
        return null;
    },

    /**
     * Check if the user is a registered user (not anonymous)
     * We need to distinguish between registered users and anonymous users.
     * Only registered users should skip CAPTCHA.
     *
     * @returns {boolean} True if the user is a registered user
     */
    isRegisteredUser() {
        // Check if we're on a profile page with user type data
        const container = document.getElementById('wallet-profile-container');
        if (container) {
            const userType = container.getAttribute('data-user-type');
            return userType === 'registered';
        }

        // If not on profile page, check for CSRF token as fallback
        // (registered users will have CSRF tokens, anonymous users might not)
        const csrfToken = this.getCsrfToken();
        return csrfToken !== null && csrfToken.length > 0;
    },

    /**
     * Check if the user is logged in (has any kind of authentication)
     * This includes both registered and anonymous users with cookies
     *
     * @returns {boolean} True if the user has any authentication
     */
    isLoggedIn: function() {
        // Check for CSRF token (indicates any kind of authentication)
        const csrfToken = this.getCsrfToken();
        return csrfToken !== null && csrfToken.length > 0;
    },

    /**
     * Logout the user
     *
     * @returns {Promise} Promise that resolves when logout is complete
     */
    logout: function() {
        return fetch(baseUrl + 'api/auth/logout', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': this.getCsrfToken() || ''
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Redirect to home page
                window.location.href = baseUrl;
            }
            return data;
        });
    },

    /**
     * Add CSRF token to fetch requests
     *
     * @param {Object} options Fetch options
     * @returns {Object} Updated fetch options with CSRF token
     */
    addCsrfToFetchOptions: function(options) {
        const csrfToken = this.getCsrfToken();
        if (!csrfToken) {
            return options;
        }

        // Create a new options object to avoid modifying the original
        const newOptions = { ...options };

        // Initialize headers if not present
        if (!newOptions.headers) {
            newOptions.headers = {};
        }

        // Add CSRF token to headers
        if (typeof newOptions.headers === 'object') {
            newOptions.headers = {
                ...newOptions.headers,
                'X-CSRF-Token': csrfToken
            };
        } else {
            // If headers is Headers object
            const headers = new Headers(newOptions.headers);
            headers.append('X-CSRF-Token', csrfToken);
            newOptions.headers = headers;
        }

        return newOptions;
    },

    /**
     * Enhanced fetch that automatically adds CSRF token
     *
     * @param {string} url URL to fetch
     * @param {Object} options Fetch options
     * @returns {Promise} Fetch promise
     */
    fetch: function(url, options = {}) {
        // Only add CSRF token for state-changing methods
        const stateChangingMethods = ['POST', 'PUT', 'DELETE', 'PATCH'];
        const method = options.method?.toUpperCase() || 'GET';

        if (stateChangingMethods.includes(method)) {
            options = this.addCsrfToFetchOptions(options);
        }

        return fetch(url, options);
    }
};

// Add logout functionality to logout buttons
document.addEventListener('DOMContentLoaded', function() {
    const logoutButtons = document.querySelectorAll('.logout-button');

    logoutButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            Auth.logout();
        });
    });

    // Dropdown menu functionality
    const userMenuButton = document.getElementById('user-menu-button');
    const userDropdownMenu = document.getElementById('user-dropdown-menu');

    if (userMenuButton && userDropdownMenu) {
        userMenuButton.addEventListener('click', function(e) {
            e.stopPropagation(); // Prevent click from immediately closing the dropdown
            userDropdownMenu.classList.toggle('show');
        });

        // Close the dropdown if the user clicks outside of it
        document.addEventListener('click', function(e) {
            if (userDropdownMenu.classList.contains('show') && !userMenuButton.contains(e.target) && !userDropdownMenu.contains(e.target)) {
                userDropdownMenu.classList.remove('show');
            }
        });
    }
});
