/**
 * Message Renderer Module
 * Handles message rendering using template-based approach for better performance and consistency
 */

const MessageRenderer = {
    // Configuration
    template: null,
    iconNames: ['user', 'user-round', 'circle-user', 'user-check', 'user-cog'],
    avatarClasses: ['ef', 'ce', 'dd', 'na', 'me'],

    /**
     * Initialize the message renderer
     */
    init: function() {
        this.template = document.getElementById('message-template');
        if (!this.template) {
            console.error('Message template not found!');
        }
    },

    /**
     * Create a message element from template and populate with data
     * @param {Object} messageData - Message data from API
     * @returns {HTMLElement} Populated message element
     */
    createMessageElement: function(messageData) {
        if (!this.template) {
            console.error('Template not initialized');
            return null;
        }

        // Clone the template content
        const messageElement = this.template.content.cloneNode(true).querySelector('.message-item');

        // Populate the message data
        this.populateMessageElement(messageElement, messageData);

        return messageElement;
    },

    /**
     * Populate a message element with data
     * @param {HTMLElement} element - Message element to populate
     * @param {Object} messageData - Message data from API
     */
    populateMessageElement: function(element, messageData) {
        // Set message ID
        element.setAttribute('data-id', messageData.id);

        // Set avatar and icon (random for visual variety)
        const avatarDiv = element.querySelector('.user-avatar');
        const iconElement = avatarDiv.querySelector('i');

        const avatarClass = this.avatarClasses[messageData.id % this.avatarClasses.length];
        const iconName = this.iconNames[messageData.id % this.iconNames.length];

        avatarDiv.className = `user-avatar ${avatarClass}`;
        iconElement.setAttribute('data-lucide', iconName);

        // Set username (real data from database)
        const authorElement = element.querySelector('.message-author');
        authorElement.textContent = messageData.sender_username || 'Anonymous';

        // User badge is hidden for now (future feature)
        const badgeElement = element.querySelector('.user-badge');
        badgeElement.style.display = 'none';
        // Future: badgeElement.textContent = 'VIP' or 'Verified' etc.

        // Set message content (escaped for security)
        const messageTextElement = element.querySelector('.message-text');
        messageTextElement.textContent = messageData.content;

        // Set timestamp
        const timestampElement = element.querySelector('.timestamp');
        const date = new Date(messageData.created_at);
        timestampElement.textContent = this.formatDate(date);
    },

    /**
     * Format date in a user-friendly way
     * @param {Date} date The date to format
     * @returns {string} Formatted date string
     */
    formatDate: function(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffSec = Math.floor(diffMs / 1000);
        const diffMin = Math.floor(diffSec / 60);
        const diffHour = Math.floor(diffMin / 60);
        const diffDay = Math.floor(diffHour / 24);

        if (diffSec < 60) {
            return 'Just now';
        } else if (diffMin < 60) {
            return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
        } else if (diffHour < 24) {
            return `${diffHour} hour${diffHour !== 1 ? 's' : ''} ago`;
        } else if (diffDay < 7) {
            return `${diffDay} day${diffDay !== 1 ? 's' : ''} ago`;
        } else {
            return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        }
    },

    /**
     * Render multiple messages into a container
     * @param {Array} messages - Array of message data objects
     * @param {HTMLElement} container - Container to render messages into
     * @param {boolean} clearContainer - Whether to clear the container first
     */
    renderMessages: function(messages, container, clearContainer = true) {
        if (clearContainer) {
            container.innerHTML = '';
        }

        if (!messages || messages.length === 0) {
            container.innerHTML = '<p class="no-messages">No comments yet. Be the first to leave a comment!</p>';
            return;
        }

        // Create document fragment for better performance
        const fragment = document.createDocumentFragment();

        messages.forEach(messageData => {
            const messageElement = this.createMessageElement(messageData);
            if (messageElement) {
                fragment.appendChild(messageElement);
            }
        });

        container.appendChild(fragment);

        // Initialize Lucide icons for the new messages
        this.initializeIcons(container);
    },

    /**
     * Add new messages to the top of a container (for auto-refresh)
     * @param {Array} newMessages - Array of new message data objects
     * @param {Array} existingMessages - Array of existing message data objects
     * @param {HTMLElement} container - Container to add messages to
     * @returns {Array} Updated array of all messages
     */
    addNewMessages: function(newMessages, existingMessages, container) {
        if (!newMessages || newMessages.length === 0) {
            return existingMessages;
        }

        // Find messages that aren't already displayed
        const existingIds = existingMessages.map(msg => msg.id);
        const messagesToAdd = newMessages.filter(msg => !existingIds.includes(msg.id));

        if (messagesToAdd.length === 0) {
            return existingMessages;
        }

        // Create fragment for new messages
        const fragment = document.createDocumentFragment();

        messagesToAdd.forEach(messageData => {
            const messageElement = this.createMessageElement(messageData);
            if (messageElement) {
                messageElement.classList.add('new-message'); // For animation
                fragment.appendChild(messageElement);
            }
        });

        // Insert new messages at the top
        if (container.firstChild) {
            container.insertBefore(fragment, container.firstChild);
        } else {
            container.appendChild(fragment);
        }

        // Initialize icons for new messages
        this.initializeIcons(container);

        // Animate new messages
        setTimeout(() => {
            const newMessageElements = container.querySelectorAll('.new-message');
            newMessageElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 50}ms`;
                element.classList.add('fade-in');
                // Remove the new-message class after animation
                setTimeout(() => {
                    element.classList.remove('new-message');
                }, 1000);
            });
        }, 0);

        // Return updated message list
        return [...newMessages];
    },

    /**
     * Initialize Lucide icons in a container
     * @param {HTMLElement} container - Container with icons to initialize
     */
    initializeIcons: function(container) {
        if (typeof lucide !== 'undefined' && lucide.createIcons) {
            // Use the simple createIcons() method which automatically finds and converts all [data-lucide] elements
            lucide.createIcons();
        } else {
            console.warn('Lucide library not available for icon initialization');
        }
    }
};

// Export for use in other modules
window.MessageRenderer = MessageRenderer;
