/**
 * Profile Page Styles
 * Contains all CSS related to wallet profile pages, messages, and user interactions
 */

/* Profile Layout */
.profile-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-lg);
    width: 100%;
}

.profile-main {
    width: 100%;
}

.profile-sidebar {
    width: 100%;
}

.activity-section {
    display: flex;
    flex-direction: column;
}

/* Message List Container */
#message-list {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: var(--spacing-sm);
    border-radius: var(--radius-md);
    scrollbar-width: thin;
    scrollbar-color: var(--color-primary) var(--color-surface-accent);
    background-color: var(--color-surface);
    padding: 2px 0;
}

#message-list::-webkit-scrollbar {
    width: 8px;
}

#message-list::-webkit-scrollbar-track {
    background: var(--color-surface-accent);
    border-radius: 4px;
}

#message-list::-webkit-scrollbar-thumb {
    background-color: var(--color-primary);
    border-radius: 4px;
}

/* Profile Sections */
.profile-section {
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    padding: 10px var(--spacing-md) var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    border: 2px solid var(--color-border);
    box-shadow: var(--shadow-md);
    width: 100%;
    box-sizing: border-box;
}

.profile-section h3 {
    margin-top: 0;
    margin-bottom: var(--spacing-md);
    font-size: 1.2rem;
    color: var(--color-text-primary);
    text-align: left;
}

.placeholder-text {
    color: var(--color-text-tertiary);
    font-style: italic;
}

/* Tab Navigation */
.tab-navigation {
    margin-bottom: var(--spacing-md);
}

.tab-links {
    display: flex;
    border-bottom: 1px solid var(--color-border);
}

.tab-link {
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--color-text-secondary);
    position: relative;
    transition: all var(--transition-normal);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
}

.tab-link:hover {
    color: var(--color-text-primary);
    text-decoration: none;
}

.tab-link.active {
    color: var(--color-primary);
    font-weight: 600;
}

.tab-link.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--color-primary);
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
}

/* Tab Content */
.tab-content {
    width: 100%;
}

/* Placeholder Content for Future Tabs */
.placeholder-content {
    text-align: center;
    padding: var(--spacing-xxl) var(--spacing-lg);
    color: var(--color-text-secondary);
}

.placeholder-icon {
    margin-bottom: var(--spacing-lg);
    opacity: 0.6;
}

.placeholder-content h4 {
    color: var(--color-text-primary);
    font-size: 1.2rem;
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.placeholder-content .placeholder-text {
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Comment Form */
.comment-form {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    background-color: var(--color-surface);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
}

/* User Avatars */
.user-avatar {
    width: 36px;
    height: 36px;
    background-color: var(--color-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    flex-shrink: 0;
    font-size: 0.9rem;
}

.user-avatar [data-lucide] {
    width: 1.2em;
    height: 1.2em;
    stroke-width: 2;
    color: white;
}

/* Avatar Color Variations */
.user-avatar.ef {
    background-color: #4f46e5; /* Indigo */
}

.user-avatar.ce {
    background-color: #0ea5e9; /* Sky blue */
}

.user-avatar.dd {
    background-color: #8b5cf6; /* Purple */
}

.user-avatar.na {
    background-color: #ec4899; /* Pink */
}

.user-avatar.me {
    background-color: var(--color-primary);
}

/* Message Form */
#add-message-form {
    flex-grow: 1;
    margin-top: 0;
}

.textarea-container {
    position: relative;
    margin-bottom: 0;
}

#add-message-form textarea {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    width: 100%;
    resize: vertical;
    min-height: 50px;
    font-family: var(--font-family);
    font-size: 0.95rem;
    line-height: 1.5;
    transition: all var(--transition-normal);
    color: var(--color-text-secondary);
    margin-bottom: 0;
}

#add-message-form textarea:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    background-color: var(--color-surface);
}

#add-message-form textarea::placeholder {
    color: var(--color-text-tertiary);
    opacity: 0.7;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 4px;
}

.post-comment-btn {
    background-color: var(--color-primary);
    color: white;
    border: none;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
}

.post-comment-btn:hover {
    background-color: var(--color-primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

.post-comment-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

/* Profile Details */
.detail-item {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--color-border);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    color: var(--color-text-secondary);
}

.detail-value {
    color: var(--color-text-primary);
    font-weight: 500;
}

.tags-container, .visitors-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.tag-placeholder, .visitor-placeholder {
    color: var(--color-text-tertiary);
    font-style: italic;
}

/* Message Items */
.message-item {
    display: flex;
    gap: var(--spacing-sm);
    padding: 10px var(--spacing-sm);
    border-bottom: 1px solid var(--color-border);
    border-left: none;
    border-right: none;
    border-top: none;
    border-radius: 0;
    margin-bottom: 0;
    transition: all var(--transition-normal);
    width: 100%;
    box-sizing: border-box;
    position: relative;
    padding-bottom: 15px;
}

.message-item:last-child {
    border-bottom: none;
    margin-bottom: 5px;
}

.message-item:hover {
    background-color: var(--color-surface-accent);
}

.message-content {
    flex-grow: 1;
    min-width: 0;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
    max-width: 100%;
    overflow: hidden;
    padding-right: 70px;
    padding-left: 4px;
}

.message-header {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-sm);
    margin-bottom: 5px;
    text-align: left;
}

.message-author {
    font-weight: 600;
    color: var(--color-text-primary);
    font-size: 0.95rem;
}

.user-badge {
    font-size: 0.8rem;
    color: var(--color-text-tertiary);
}

.message-text {
    margin-top: 5px;
    line-height: 1.4;
    color: var(--color-text-secondary);
    font-size: 0.9rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
    text-align: left;
    padding-left: 2px;
}

.message-item .timestamp {
    color: var(--color-text-tertiary);
    font-size: 0.8rem;
    position: absolute;
    top: 10px;
    right: var(--spacing-sm);
    text-align: right;
}

/* Profile Page CAPTCHA Section */
.profile-content .captcha-section {
    margin: 15px 0;
    padding: 0;
    text-align: left;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.profile-content .captcha-section .cf-turnstile {
    text-align: left;
    display: block;
}

.profile-content .captcha-hint {
    margin: 0;
    font-size: 10px;
    color: #888;
    font-style: italic;
    text-align: left;
    opacity: 0.8;
}

/* When CAPTCHA is visible, adjust form layout (profile page only) */
.profile-content .captcha-section:not([style*="display: none"]) ~ .form-actions {
    margin-top: -45px;
    margin-left: 320px;
    display: flex;
    justify-content: flex-end; /* Right-align the button */
    width: calc(100% - 320px); /* Take remaining width after CAPTCHA */
}

.profile-content .captcha-section:not([style*="display: none"]) ~ .form-actions .post-comment-btn {
    margin-top: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-content {
        grid-template-columns: 1fr;
    }

    .profile-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .profile-header-actions {
        width: 100%;
        justify-content: space-between;
    }

    .profile-content .captcha-section:not([style*="display: none"]) ~ .form-actions {
        margin-top: 4px;
        margin-left: 0;
        justify-content: flex-end;
    }
}
