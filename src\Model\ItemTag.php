<?php

namespace App\Model;

use App\Core\Database;
use PDO;
use PDOException;

/**
 * Model for interacting with the 'item_tags' table.
 */
class ItemTag
{
    private PDO $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Add a tag to an item.
     *
     * @param int $tagId
     * @param string $itemId
     * @param string $itemType
     * @param string $taggedByUserId
     * @param bool $taggerIsAnonymous
     * @return bool Success status
     */
    public function addTag(int $tagId, string $itemId, string $itemType, string $taggedByUserId, bool $taggerIsAnonymous): bool
    {
        try {
            $sql = "INSERT INTO item_tags (tag_id, item_id, item_type, tagged_by_user_id, tagger_is_anonymous, created_at) 
                    VALUES (:tag_id, :item_id, :item_type, :tagged_by_user_id, :tagger_is_anonymous, NOW())";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tag_id', $tagId, PDO::PARAM_INT);
            $stmt->bindParam(':item_id', $itemId, PDO::PARAM_STR);
            $stmt->bindParam(':item_type', $itemType, PDO::PARAM_STR);
            $stmt->bindParam(':tagged_by_user_id', $taggedByUserId, PDO::PARAM_STR);
            $stmt->bindParam(':tagger_is_anonymous', $taggerIsAnonymous, PDO::PARAM_BOOL);
            $stmt->execute();
            
            return true;
        } catch (PDOException $e) {
            error_log("Database Error adding tag: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if a user has already tagged an item with a specific tag.
     *
     * @param int $tagId
     * @param string $itemId
     * @param string $itemType
     * @param string $userId
     * @param bool $isAnonymous
     * @return bool
     */
    public function hasUserTaggedItem(int $tagId, string $itemId, string $itemType, string $userId, bool $isAnonymous): bool
    {
        try {
            $sql = "SELECT COUNT(*) FROM item_tags 
                    WHERE tag_id = :tag_id 
                    AND item_id = :item_id 
                    AND item_type = :item_type 
                    AND tagged_by_user_id = :user_id 
                    AND tagger_is_anonymous = :is_anonymous";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tag_id', $tagId, PDO::PARAM_INT);
            $stmt->bindParam(':item_id', $itemId, PDO::PARAM_STR);
            $stmt->bindParam(':item_type', $itemType, PDO::PARAM_STR);
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_STR);
            $stmt->bindParam(':is_anonymous', $isAnonymous, PDO::PARAM_BOOL);
            $stmt->execute();
            
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            error_log("Database Error checking user tag: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all tags for an item with their counts.
     *
     * @param string $itemId
     * @param string $itemType
     * @return array Array of tags with counts
     */
    public function getTagsForItem(string $itemId, string $itemType): array
    {
        try {
            $sql = "SELECT t.id, t.name, t.description, COUNT(it.tag_id) as count
                    FROM tags t
                    INNER JOIN item_tags it ON t.id = it.tag_id
                    WHERE it.item_id = :item_id AND it.item_type = :item_type
                    GROUP BY t.id, t.name, t.description
                    ORDER BY count DESC, t.name ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':item_id', $itemId, PDO::PARAM_STR);
            $stmt->bindParam(':item_type', $itemType, PDO::PARAM_STR);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Database Error getting tags for item: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get tags that a specific user has applied to an item.
     *
     * @param string $itemId
     * @param string $itemType
     * @param string $userId
     * @param bool $isAnonymous
     * @return array Array of tag IDs
     */
    public function getUserTagsForItem(string $itemId, string $itemType, string $userId, bool $isAnonymous): array
    {
        try {
            $sql = "SELECT tag_id FROM item_tags 
                    WHERE item_id = :item_id 
                    AND item_type = :item_type 
                    AND tagged_by_user_id = :user_id 
                    AND tagger_is_anonymous = :is_anonymous";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':item_id', $itemId, PDO::PARAM_STR);
            $stmt->bindParam(':item_type', $itemType, PDO::PARAM_STR);
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_STR);
            $stmt->bindParam(':is_anonymous', $isAnonymous, PDO::PARAM_BOOL);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (PDOException $e) {
            error_log("Database Error getting user tags for item: " . $e->getMessage());
            return [];
        }
    }
}
