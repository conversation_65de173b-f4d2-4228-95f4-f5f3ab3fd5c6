<?php

namespace App\Helpers;

use Exception;

/**
 * JWT Helper class
 *
 * Provides JWT functionality for authentication
 */
class JwtHelper
{
    // JWT configuration
    private static $secretKey;
    private static $algorithm = 'HS256';
    private static $issuer = 'https://crypt.ag';
    private static $audience = 'https://crypt.ag';

    /**
     * Initialize the JWT configuration
     */
    public static function init()
    {
        $config = require __DIR__ . '/../../config/config.php';
        self::$secretKey = $config['auth']['jwt_secret'] ?? 'your-secret-key-here';
    }

    // JWT expiration times (in seconds)
    const REGISTERED_USER_EXPIRY = 21600; // 6 hours
    const ANONYMOUS_USER_EXPIRY = 2592000; // 30 days

    // <PERSON>ie names
    const AUTH_COOKIE_NAME = 'auth_token';
    const CSRF_COOKIE_NAME = 'csrf_token';

    /**
     * Generate a JWT for a registered user
     *
     * @param string $userId User ID
     * @param string $username Username
     * @param bool $isActive Whether the user is active
     * @return string JWT token
     */
    public static function generateRegisteredUserJwt(string $userId, string $username, bool $isActive): string
    {
        $issuedAt = time();
        $expirationTime = $issuedAt + self::REGISTERED_USER_EXPIRY;

        $payload = [
            'iss' => self::$issuer,
            'aud' => self::$audience,
            'iat' => $issuedAt,
            'nbf' => $issuedAt,
            'exp' => $expirationTime,
            'jti' => bin2hex(random_bytes(16)),
            'sub' => $userId,
            'username' => $username,
            'type' => 'registered',
            'active' => $isActive,
            'lar' => $issuedAt // Last Activity Recorded
        ];

        return self::encode($payload);
    }

    /**
     * Generate a JWT for an anonymous user
     *
     * @param string $userId User ID
     * @param string $username Generated username
     * @param bool $isActive Whether the user is active
     * @return string JWT token
     */
    public static function generateAnonymousUserJwt(string $userId, string $username, bool $isActive): string
    {
        $issuedAt = time();
        $expirationTime = $issuedAt + self::ANONYMOUS_USER_EXPIRY;

        $payload = [
            'iss' => self::$issuer,
            'aud' => self::$audience,
            'iat' => $issuedAt,
            'nbf' => $issuedAt,
            'exp' => $expirationTime,
            'jti' => bin2hex(random_bytes(16)),
            'sub' => $userId,
            'username' => $username,
            'type' => 'anonymous_verified',
            'active' => $isActive,
            'lar' => $issuedAt // Last Activity Recorded
        ];

        return self::encode($payload);
    }

    /**
     * Validate a JWT and return the payload if valid
     *
     * @param string $jwt JWT token
     * @return array|false Payload if valid, false otherwise
     */
    public static function validateJwt(string $jwt)
    {
        try {
            $payload = self::decode($jwt);

            // Verify issuer and audience
            if ($payload['iss'] !== self::$issuer || $payload['aud'] !== self::$audience) {
                return false;
            }

            // Verify expiration and not before
            $now = time();
            if ($now < $payload['nbf'] || $now > $payload['exp']) {
                return false;
            }

            return $payload;
        } catch (Exception $e) {
            error_log("JWT validation error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Set the JWT auth cookie
     *
     * @param string $jwt JWT token
     * @param int $expiry Expiration time in seconds
     * @return bool True if successful
     */
    public static function setAuthCookie(string $jwt, int $expiry): bool
    {
        // Only use secure cookies on HTTPS
        $secure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
        $httpOnly = true;

        return setcookie(
            self::AUTH_COOKIE_NAME,
            $jwt,
            [
                'expires' => time() + $expiry,
                'path' => '/',
                'domain' => '',
                'secure' => $secure,
                'httponly' => $httpOnly,
                'samesite' => 'Lax'
            ]
        );
    }

    /**
     * Set the CSRF token cookie
     *
     * @param string $csrfToken CSRF token
     * @param int $expiry Expiration time in seconds
     * @return bool True if successful
     */
    public static function setCsrfCookie(string $csrfToken, int $expiry): bool
    {
        // Only use secure cookies on HTTPS
        $secure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
        $httpOnly = false; // Must be accessible from JavaScript

        return setcookie(
            self::CSRF_COOKIE_NAME,
            $csrfToken,
            [
                'expires' => time() + $expiry,
                'path' => '/',
                'domain' => '',
                'secure' => $secure,
                'httponly' => $httpOnly,
                'samesite' => 'Lax'
            ]
        );
    }

    /**
     * Clear the auth and CSRF cookies
     *
     * @return bool True if successful
     */
    public static function clearAuthCookies(): bool
    {
        // Only use secure cookies on HTTPS
        $secure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';

        $authCookieCleared = setcookie(
            self::AUTH_COOKIE_NAME,
            '',
            [
                'expires' => time() - 3600,
                'path' => '/',
                'domain' => '',
                'secure' => $secure,
                'httponly' => true,
                'samesite' => 'Lax'
            ]
        );

        $csrfCookieCleared = setcookie(
            self::CSRF_COOKIE_NAME,
            '',
            [
                'expires' => time() - 3600,
                'path' => '/',
                'domain' => '',
                'secure' => $secure,
                'httponly' => false,
                'samesite' => 'Lax'
            ]
        );

        return $authCookieCleared && $csrfCookieCleared;
    }

    /**
     * Generate a CSRF token
     *
     * @return string CSRF token
     */
    public static function generateCsrfToken(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Validate a CSRF token
     *
     * @param string $token Token from header
     * @param string $cookieToken Token from cookie
     * @return bool True if valid
     */
    public static function validateCsrfToken(string $token, string $cookieToken): bool
    {
        return hash_equals($token, $cookieToken);
    }

    /**
     * Encode a payload into a JWT
     *
     * @param array $payload Payload to encode
     * @return string JWT token
     */
    private static function encode(array $payload): string
    {
        // Initialize if not already initialized
        if (self::$secretKey === null) {
            self::init();
        }

        $header = [
            'typ' => 'JWT',
            'alg' => self::$algorithm
        ];

        $headerEncoded = self::base64UrlEncode(json_encode($header));
        $payloadEncoded = self::base64UrlEncode(json_encode($payload));

        $signature = hash_hmac(
            'sha256',
            "$headerEncoded.$payloadEncoded",
            self::$secretKey,
            true
        );

        $signatureEncoded = self::base64UrlEncode($signature);

        return "$headerEncoded.$payloadEncoded.$signatureEncoded";
    }

    /**
     * Decode a JWT and return the payload
     *
     * @param string $jwt JWT token
     * @return array Payload
     * @throws Exception If the JWT is invalid
     */
    private static function decode(string $jwt): array
    {
        // Initialize if not already initialized
        if (self::$secretKey === null) {
            self::init();
        }

        $parts = explode('.', $jwt);

        if (count($parts) !== 3) {
            throw new Exception('Invalid JWT format');
        }

        list($headerEncoded, $payloadEncoded, $signatureEncoded) = $parts;

        $signature = self::base64UrlDecode($signatureEncoded);

        $expectedSignature = hash_hmac(
            'sha256',
            "$headerEncoded.$payloadEncoded",
            self::$secretKey,
            true
        );

        if (!hash_equals($expectedSignature, $signature)) {
            throw new Exception('Invalid JWT signature');
        }

        $payload = json_decode(self::base64UrlDecode($payloadEncoded), true);

        if (!is_array($payload)) {
            throw new Exception('Invalid JWT payload');
        }

        return $payload;
    }

    /**
     * Base64 URL encode
     *
     * @param string $data Data to encode
     * @return string Base64 URL encoded string
     */
    private static function base64UrlEncode(string $data): string
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * Base64 URL decode
     *
     * @param string $data Data to decode
     * @return string Decoded data
     */
    private static function base64UrlDecode(string $data): string
    {
        return base64_decode(strtr($data, '-_', '+/'));
    }

    /**
     * Get the currently authenticated user (RegisteredUser or AnonymousUser) from the auth_token cookie.
     *
     * @return \App\Model\RegisteredUser|\App\Model\AnonymousUser|null Returns the user object if authenticated, null otherwise.
     */
    public static function getCurrentUser(): ?\App\Model\User
    {
        if (!isset($_COOKIE[self::AUTH_COOKIE_NAME])) {
            return null;
        }

        $jwt = $_COOKIE[self::AUTH_COOKIE_NAME];
        $payload = self::validateJwt($jwt);

        if (!$payload) {
            // Invalid or expired JWT, clear cookies
            self::clearAuthCookies();
            return null;
        }

        // Check if the token is for a registered or anonymous user
        $userId = $payload['sub'] ?? null;
        $userType = $payload['type'] ?? null;
        $isActive = $payload['active'] ?? false;

        if (!$userId || !$userType || !$isActive) {
            self::clearAuthCookies();
            return null;
        }

        if ($userType === 'registered') {
            $user = \App\Model\User::getById($userId, 'registered');
            if ($user && $user->isActive()) {
                return $user;
            }
        } elseif ($userType === 'anonymous_verified') {
            $user = \App\Model\User::getById($userId, 'anonymous_verified');
            if ($user && $user->isActive()) {
                return $user;
            }
        }

        // If user not found or not active, clear cookies
        self::clearAuthCookies();
        return null;
    }
}
