<?php

namespace App\Service;

/**
 * CaptchaService
 *
 * Handles CAPTCHA verification, specifically for Cloudflare Turnstile.
 */
class CaptchaService
{
    private $config;

    public function __construct()
    {
        // Load configuration directly. For more complex apps, consider dependency injection.
        $this->config = require __DIR__ . '/../../config/config.php';
    }

    /**
     * Verify a CAPTCHA token with Cloudflare Turnstile
     *
     * @param string $token CAPTCHA token
     * @param string $remoteIp Remote IP address
     * @return bool True if verification succeeded
     */
    public function verifyTurnstileToken(string $token, string $remoteIp): bool
    {
        $secretKey = $this->config['auth']['cloudflare_turnstile']['secret_key'] ?? '';
        $appUrl = $this->config['app']['url'] ?? 'http://localhost/CRYPTAG';

        if (empty($secretKey)) {
            error_log('CAPTCHA verification failed: Turnstile secret key is not configured.');
            return false;
        }

        // Extract domain from app URL for the idempotency key
        $parsedUrl = parse_url($appUrl);
        $domain = $parsedUrl['host'] ?? 'localhost';

        // Log the domain being used
        error_log('Using domain for CAPTCHA verification (CaptchaService): ' . $domain);

        // Generate a unique idempotency key based on token and domain
        $idempotencyKey = md5($token . $domain);

        $url = 'https://challenges.cloudflare.com/turnstile/v0/siteverify';
        $data = [
            'secret' => $secretKey,
            'response' => $token,
            'remoteip' => $remoteIp,
            'idempotency_key' => $idempotencyKey
        ];

        // Debug log the verification attempt
        error_log('CAPTCHA verification attempt (CaptchaService): Token prefix=' . substr($token, 0, 10) .
                  '..., IP=' . $remoteIp .
                  ', Domain=' . $domain .
                  ', SecretKey present: Yes');

        $options = [
            'http' => [
                'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => http_build_query($data),
                'ignore_errors' => true // Get HTTP error bodies
            ]
        ];

        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);

        if ($result === false) {
            error_log('CAPTCHA verification failed (CaptchaService): file_get_contents returned false. Could not connect to Cloudflare or other stream error.');
            return false;
        }

        $responseData = json_decode($result, true);

        if (!is_array($responseData)) {
            error_log('CAPTCHA verification failed (CaptchaService): Invalid JSON response from Cloudflare. HTTP Status from response headers (if available): ' . ($http_response_header[0] ?? 'N/A') . '. Response body: ' . $result);
            return false;
        }

        if (!isset($responseData['success']) || $responseData['success'] !== true) {
            error_log('CAPTCHA verification failed (CaptchaService): Cloudflare reported failure. ResponseData: ' . json_encode($responseData));
            $errorCodes = $responseData['error-codes'] ?? [];
            foreach ($errorCodes as $errorCode) {
                error_log('CAPTCHA error code (CaptchaService): ' . $errorCode);
                if ($errorCode === 'invalid-domain') {
                    error_log('Domain validation failed (CaptchaService). Make sure ' . $domain . ' is added to your Cloudflare Turnstile settings.');
                }
            }
            return false;
        }

        error_log('CAPTCHA verification successful (CaptchaService) for token prefix: ' . substr($token, 0, 10) . '...');
        return true;
    }
}