/* Roadmap Page Specific Styles - Aligned with main theme */

/*
   General body, font, and global link styles are inherited from style.css.
   We only define styles specific to the roadmap page structure here.
*/

.roadmap-page .container { /* More specific selector for roadmap container */
    width: 80%;
    max-width: 1000px;
    margin: var(--spacing-xl) auto; /* Use theme spacing */
    padding: var(--spacing-xl);    /* Use theme spacing */
    background-color: var(--color-surface); /* Use theme surface color */
    border-radius: var(--radius-lg); /* Use theme radius */
    box-shadow: var(--shadow-lg);    /* Use theme shadow */
    border: 1px solid var(--color-border);
}

/* Headings will inherit from style.css, but we can add specific roadmap accents */
.roadmap-page h1 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    font-size: 2.5em; /* Can be adjusted if needed */
    color: var(--color-text-primary); /* Main text color for emphasis */
}

.roadmap-page h2 {
    font-size: 1.8em;
    color: var(--color-primary); /* Use theme primary color for main section titles */
    border-bottom: 2px solid var(--color-border);
    padding-bottom: var(--spacing-sm);
    margin-top: var(--spacing-xl);
    margin-bottom: var(--spacing-md);
}

.roadmap-page h2 em {
    font-size: 0.8em;
    color: var(--color-text-secondary);
    font-weight: normal;
}

.roadmap-page h3 {
    font-size: 1.4em;
    color: var(--color-secondary); /* Use theme secondary color for sub-headers */
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.roadmap-page p {
    margin-bottom: var(--spacing-md);
    color: var(--color-text-secondary); /* Consistent paragraph color */
}

.roadmap-page ul {
    list-style-type: none;
    padding-left: 0;
}

.roadmap-page ul li {
    background-color: var(--color-surface-accent); /* Slightly different background for list items */
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border-left: 5px solid var(--color-primary); /* Theme primary accent */
}

.roadmap-page ul li strong {
    color: var(--color-text-primary); /* Ensure strong text is primary */
    font-weight: 600;
}

/* Roadmap Specific Sections */
.roadmap-header p {
    text-align: center;
    font-style: italic;
    color: var(--color-text-tertiary);
}
.roadmap-header p strong {
     color: var(--color-text-secondary);
     font-style: normal;
}


.roadmap-footer p {
    text-align: center;
    font-style: italic;
    color: var(--color-text-tertiary);
    margin-top: var(--spacing-lg);
}

.roadmap-quarter {
    /* Inherits from .card */
    margin-bottom: var(--spacing-xl); /* Keep specific margin */
    padding: var(--spacing-lg); /* Keep specific padding */
}

.roadmap-quarter h2 {
    border-bottom: none;
    padding-bottom: 0;
    margin-top: 0;
}

.roadmap-quarter .focus-text {
    font-style: italic;
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-md);
    display: block;
}
.roadmap-quarter .focus-text strong {
    font-style: normal;
    color: var(--color-text-primary);
}


.key-features {
    margin-top: var(--spacing-sm);
}

.key-features li {
    background-color: var(--color-surface-accent);
    border-left-color: var(--color-secondary); /* Theme secondary accent for feature list items */
}

.ongoing-section h2 { /* Changed from h3 to h2 for consistency with quarters */
    color: var(--color-primary);
}

.ongoing-section ul li {
    border-left-color: var(--color-text-tertiary); /* A neutral accent for ongoing items */
    background-color: var(--color-surface-accent);
}

/* Horizontal Rule */
.roadmap-page hr {
    border: 0;
    height: 1px;
    background-image: linear-gradient(to right, rgba(var(--color-primary-rgb, 59, 130, 246), 0), rgba(var(--color-primary-rgb, 59, 130, 246), 0.5), rgba(var(--color-primary-rgb, 59, 130, 246), 0));
    /* Note: --color-primary-rgb would need to be defined in :root if you want to use rgba with CSS vars directly like this, e.g. --color-primary-rgb: 59, 130, 246;
       Alternatively, use a solid border color:
       border-top: 1px solid var(--color-border);
       background-image: none;
    */
    border-top: 1px solid var(--color-border); /* Simpler approach */
    background-image: none; /* Remove gradient if using solid border */
    margin: var(--spacing-xl) 0;
}


/* Responsive adjustments from original roadmap.css can be kept if they don't conflict,
   but ensure they use theme variables for consistency if modifying layout aspects.
   The .container in style.css already has responsive width adjustments.
*/
@media (max-width: 768px) {
    .roadmap-page .container { /* Ensure specificity */
        width: 95%;
        padding: var(--spacing-lg);
    }

    .roadmap-page h1 {
        font-size: 2em;
    }

    .roadmap-page h2 {
        font-size: 1.5em;
    }

    .roadmap-page h3 {
        font-size: 1.2em;
    }
}