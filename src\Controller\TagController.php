<?php

namespace App\Controller;

use App\Core\Request;
use App\Model\Tag;
use App\Model\ItemTag;
use App\Model\Wallet;
use App\Model\RateLimit;
use App\Helpers\JwtHelper;
use Exception;

/**
 * Controller for handling tag-related operations.
 */
class TagController extends BaseController
{
    private Tag $tagModel;
    private ItemTag $itemTagModel;
    private Wallet $walletModel;

    public function __construct()
    {
        $this->tagModel = new Tag();
        $this->itemTagModel = new ItemTag();
        $this->walletModel = new Wallet();
    }

    /**
     * Add a tag to a wallet.
     * POST /api/tags/add
     */
    public function addTag(): void
    {
        // Only accept POST requests
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->sendJsonResponse(['success' => false, 'error' => 'Method not allowed'], 405);
            return;
        }

        // Get current user
        $currentUser = JwtHelper::getCurrentUser();
        if (!$currentUser) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Authentication required'], 401);
            return;
        }

        // Rate limiting
        $isAnonymous = $currentUser->getType() === 'anonymous_verified';

        if (RateLimit::isTagAttemptLimitExceeded($currentUser->getId(), $isAnonymous)) {
            $this->sendJsonResponse([
                'success' => false,
                'error' => 'Rate limit exceeded. Please wait before adding another tag.'
            ], 429);
            return;
        }

        try {

            // Get POST data
            $data = json_decode(file_get_contents('php://input'), true);
            if (!$data) {
                $data = $_POST;
            }

            $tagId = $data['tag_id'] ?? null;
            $walletId = $data['wallet_id'] ?? null;

            if (!$tagId || !$walletId || !is_numeric($tagId) || !is_numeric($walletId)) {
                $this->sendJsonResponse(['success' => false, 'error' => 'Invalid tag ID or wallet ID'], 400);
                return;
            }

            $tagId = (int)$tagId;
            $walletId = (int)$walletId;

            // Check if wallet exists and tags are allowed
            $wallet = $this->walletModel->findByIdWithTagInfo($walletId);
            if (!$wallet) {
                $this->sendJsonResponse(['success' => false, 'error' => 'Wallet not found'], 404);
                return;
            }

            if (!$wallet['are_tags_allowed']) {
                $this->sendJsonResponse(['success' => false, 'error' => 'Tags are not allowed for this wallet'], 403);
                return;
            }

            // Check if tag exists and can be used for wallet_profile
            if (!$this->tagModel->canBeUsedForItemType($tagId, 'wallet_profile')) {
                $this->sendJsonResponse(['success' => false, 'error' => 'Tag cannot be used for wallet profiles'], 400);
                return;
            }

            // Check if user has already tagged this wallet with this tag
            if ($this->itemTagModel->hasUserTaggedItem(
                $tagId,
                (string)$walletId,
                'wallet_profile',
                $currentUser->getId(),
                $isAnonymous
            )) {
                $this->sendJsonResponse(['success' => false, 'error' => 'You have already applied this tag'], 409);
                return;
            }

            // Add the tag
            $success = $this->itemTagModel->addTag(
                $tagId,
                (string)$walletId,
                'wallet_profile',
                $currentUser->getId(),
                $isAnonymous
            );

            if ($success) {
                // Record the rate limit attempt
                RateLimit::recordAttempt($currentUser->getId(), RateLimit::TYPE_USER_TAG_ATTEMPT);

                $this->sendJsonResponse(['success' => true, 'message' => 'Tag added successfully']);
            } else {
                $this->sendJsonResponse(['success' => false, 'error' => 'Failed to add tag'], 500);
            }
        } catch (Exception $e) {
            error_log("Error adding tag: " . $e->getMessage());
            $this->sendJsonResponse(['success' => false, 'error' => 'Failed to add tag due to server error'], 500);
        }
    }

    /**
     * Get all tag data for a wallet (available tags with user status + popular tags).
     * This replaces the need for multiple API calls.
     * GET /api/tags/wallet-data/{walletId}
     */
    public function getWalletTagData(string $walletId): void
    {
        if (!is_numeric($walletId)) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Invalid wallet ID'], 400);
            return;
        }

        $walletId = (int)$walletId;

        try {
            // Get current user
            $currentUser = JwtHelper::getCurrentUser();
            if (!$currentUser) {
                $this->sendJsonResponse(['success' => false, 'error' => 'Authentication required'], 401);
                return;
            }

            // Check if wallet exists and tags are allowed
            $wallet = $this->walletModel->findByIdWithTagInfo($walletId);
            if (!$wallet) {
                $this->sendJsonResponse(['success' => false, 'error' => 'Wallet not found'], 404);
                return;
            }

            if (!$wallet['are_tags_allowed']) {
                $this->sendJsonResponse([
                    'success' => true,
                    'tags_allowed' => false,
                    'available_tags' => [],
                    'popular_tags' => []
                ]);
                return;
            }

            // Get available tags for wallet_profile
            $availableTags = $this->tagModel->getAvailableTagsForItemType('wallet_profile');

            // Get user's applied tags for this wallet
            $userTagIds = $this->itemTagModel->getUserTagsForItem(
                (string)$walletId,
                'wallet_profile',
                $currentUser->getId(),
                $currentUser->getType() === 'anonymous_verified'
            );

            // Combine available tags with user status
            $tagsWithStatus = [];
            foreach ($availableTags as $tag) {
                $tagsWithStatus[] = [
                    'id' => $tag['id'],
                    'name' => $tag['name'],
                    'description' => $tag['description'],
                    'user_applied' => in_array($tag['id'], $userTagIds)
                ];
            }

            // Get popular tags (tags applied to this wallet with counts)
            $popularTags = $this->itemTagModel->getTagsForItem((string)$walletId, 'wallet_profile');

            $this->sendJsonResponse([
                'success' => true,
                'tags_allowed' => true,
                'available_tags' => $tagsWithStatus,
                'popular_tags' => $popularTags
            ]);

        } catch (Exception $e) {
            error_log("Error getting wallet tag data: " . $e->getMessage());
            $this->sendJsonResponse(['success' => false, 'error' => 'Failed to load tag data'], 500);
        }
    }
}
