<?php
// Ensure variables are set and escape output
$address = isset($wallet['address']) ? htmlspecialchars($wallet['address'], ENT_QUOTES, 'UTF-8') : 'N/A';
$type = isset($wallet['type']) ? strtoupper(htmlspecialchars($wallet['type'], ENT_QUOTES, 'UTF-8')) : 'N/A';
$walletId = isset($wallet['id']) ? htmlspecialchars($wallet['id'], ENT_QUOTES, 'UTF-8') : '';
$rawType = isset($wallet['type']) ? rawurlencode($wallet['type']) : '';
$rawAddress = isset($wallet['address']) ? rawurlencode($wallet['address']) : '';

// Pass raw type/address for JS data attributes
$jsWalletType = isset($wallet['type']) ? htmlspecialchars($wallet['type'], ENT_QUOTES, 'UTF-8') : '';
$jsWalletAddress = isset($wallet['address']) ? htmlspecialchars($wallet['address'], ENT_QUOTES, 'UTF-8') : '';

// Create truncated address for display (show first 6 and last 4 characters)
$truncatedAddress = '';
if (strlen($address) > 10) {
    $truncatedAddress = substr($address, 0, 6) . '...' . substr($address, -4);
} else {
    $truncatedAddress = $address; // If address is too short, don't truncate
}
?>

<!-- Add data attributes to a container for JS access -->
<div id="wallet-profile-container"
     data-wallet-id="<?= $walletId ?>"
     data-wallet-type="<?= $jsWalletType ?>"
     data-wallet-address="<?= $jsWalletAddress ?>"
     data-user-type="<?= $currentUser ? htmlspecialchars($currentUser->getType(), ENT_QUOTES, 'UTF-8') : 'anonymous' ?>">

    <div class="profile-header">
        <div class="profile-header-left">
            <div class="wallet-icon">
                <!-- Use Lucide icon for the wallet type -->
                <?php
                // Use icons from the Lucide icon list
                $iconName = 'wallet';
                switch(strtolower($type)) {
                    case 'eth':
                        $iconName = 'ethereum'; // Ethereum icon is available in Lucide
                        break;
                    case 'btc':
                        $iconName = 'bitcoin'; // Bitcoin icon is available in Lucide
                        break;
                    case 'sol':
                        $iconName = 'sun'; // Using sun for Solana
                        break;
                }
                ?>
                <i data-lucide="<?= $iconName ?>"></i>
            </div>
            <div class="wallet-info">
                <h2 title="<?= $address ?>"><?= $truncatedAddress ?></h2>
                <p class="wallet-network"><?= $type ?> Network</p>
            </div>
        </div>
        <div class="profile-header-actions">
            <button class="action-button copy-address" title="Copy full address to clipboard">
                <span class="button-icon"><i data-lucide="clipboard-copy"></i></span> Copy Address
            </button>
            <button class="action-button view-explorer" title="View on blockchain explorer">
                <span class="button-icon"><i data-lucide="external-link"></i></span> View on Explorer
            </button>

            <!-- Tag Button with Dropdown -->
            <div class="tag-dropdown">
                <button id="tag-button" class="tag-button" title="Add tags to this wallet">
                    <i data-lucide="tag"></i> Tag
                </button>
                <div id="tag-dropdown-content" class="tag-dropdown-content">
                    <div class="tag-dropdown-header">
                        <h4 class="tag-dropdown-title">Add Tag</h4>
                        <p class="tag-dropdown-subtitle">Select a tag to apply to this wallet</p>
                    </div>
                    <div id="available-tags" class="available-tags">
                        <!-- Tags will be loaded here by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="profile-content">
        <div class="profile-main">
            <div class="profile-section bio-section">
                <h3>Bio</h3>
                <p class="placeholder-text">
                    No bio available. For now!
                </p>
            </div>

            <div class="profile-section activity-section">
                <div class="tab-navigation">
                    <div class="tab-links">
                        <a href="#" class="tab-link active" data-tab="comments">Comments</a>
                        <a href="#" class="tab-link" data-tab="activity">Activity</a>
                        <a href="#" class="tab-link" data-tab="mentions">Mentions</a>
                    </div>
                </div>

                <!-- Comments Tab Content -->
                <div class="tab-content" id="comments-tab" style="display: block;">
                    <div class="comment-form">
                        <div class="user-avatar me">
                            <i data-lucide="user-round-plus"></i>
                        </div>
                        <form id="add-message-form" method="POST">
                            <input type="hidden" name="wallet_id" value="<?= $walletId ?>">
                            <div class="textarea-container">
                                <textarea name="message_content" placeholder="Write a comment..." rows="2"></textarea>
                            </div>
                            <div class="captcha-section" style="display: none;">
                                <div id="cf-turnstile-container" class="cf-turnstile"></div>
                                <p class="captcha-hint">Please complete the CAPTCHA to post your message</p>
                                <input type="hidden" id="cf-turnstile-response" name="cf-turnstile-response">
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="post-comment-btn">Post Comment</button>
                            </div>
                        </form>
                    </div>

                    <!-- Hidden message template for cloning -->
                    <template id="message-template">
                        <div class="message-item" data-id="">
                            <div class="user-avatar">
                                <i data-lucide="user"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-header">
                                    <div class="message-author"></div>
                                    <div class="user-badge" style="display: none;"></div>
                                </div>
                                <div class="message-text"></div>
                            </div>
                            <div class="timestamp"></div>
                        </div>
                    </template>

                    <div id="message-list">
                        <div class="loading-container">
                            <span class="loading"></span>
                            <span>Loading comments...</span>
                        </div>
                    </div>

                    <div id="pagination-controls"></div>
                </div>

                <!-- Activity Tab Content -->
                <div class="tab-content" id="activity-tab" style="display: none;">
                    <div class="placeholder-content">
                        <div class="placeholder-icon">
                            <i data-lucide="activity" style="width: 48px; height: 48px; color: var(--color-text-tertiary);"></i>
                        </div>
                        <h4>Activity Feed Coming Soon</h4>
                        <p class="placeholder-text">
                            Track wallet activity, transactions, and interactions across the blockchain.
                            This feature is currently in development.
                        </p>
                    </div>
                </div>

                <!-- Mentions Tab Content -->
                <div class="tab-content" id="mentions-tab" style="display: none;">
                    <div class="placeholder-content">
                        <div class="placeholder-icon">
                            <i data-lucide="at-sign" style="width: 48px; height: 48px; color: var(--color-text-tertiary);"></i>
                        </div>
                        <h4>Mentions Coming Soon</h4>
                        <p class="placeholder-text">
                            See when this wallet address is mentioned in comments and discussions.
                            This feature is currently in development.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="profile-sidebar">
            <div class="profile-section details-section">
                <h3>Details</h3>
                <div class="detail-item">
                    <span class="detail-label">First Seen:</span>
                    <span class="detail-value">Unknown</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Balance:</span>
                    <span class="detail-value">Loading...</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Transactions:</span>
                    <span class="detail-value">Loading...</span>
                </div>
            </div>

            <div class="profile-section tags-section">
                <h3>Popular Tags</h3>
                <div class="tags-container">
                    <span class="tag-placeholder">No tags yet</span>
                </div>
            </div>

            <div class="profile-section visitors-section">
                <h3>Recent Visitors</h3>
                <div class="visitors-container">
                    <span class="visitor-placeholder">No recent visitors</span>
                </div>
            </div>
        </div>
    </div>
</div> <!-- Close wallet-profile-container -->

<!-- Tag Confirmation Popover -->
<div id="tag-overlay" class="tag-overlay"></div>
<div id="tag-confirmation-popover" class="tag-confirmation-popover">
    <div class="tag-confirmation-header">
        <h4 class="tag-confirmation-title">Confirm Tag</h4>
        <p class="tag-confirmation-message">
            Are you sure you want to add the tag <span id="confirmation-tag-name" class="tag-confirmation-tag"></span> to this wallet?
            <br><small>Note: Tags cannot be removed once added.</small>
        </p>
    </div>
    <div class="tag-confirmation-actions">
        <button id="tag-cancel-btn" class="tag-cancel-btn">Cancel</button>
        <button id="tag-confirm-btn" class="tag-confirm-btn">Add Tag</button>
    </div>
</div>
