<?php
// Calculate base URL dynamically to handle subdirectory deployments
$scriptDir = dirname($_SERVER['SCRIPT_NAME']); // e.g., /CRYPTAG/public or /public
$baseUrl = dirname($scriptDir); // e.g., /CRYPTAG or /
// Ensure it ends with a slash if it's not the root, and handle Windows backslashes
$baseUrl = rtrim(str_replace('\\', '/', $baseUrl), '/');
$baseUrl = ($baseUrl === '') ? '/' : $baseUrl . '/'; // Ensure root is '/' and others end with '/'
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Use htmlspecialchars for the title to prevent XSS -->
    <title><?= isset($pageTitle) ? htmlspecialchars($pageTitle, ENT_QUOTES, 'UTF-8') : 'Cryptag' ?></title>
    <!-- Link to the main stylesheet using the calculated base URL -->
    <link rel="stylesheet" href="<?= $baseUrl ?>css/style.css">
    <link rel="stylesheet" href="<?= $baseUrl ?>css/profile.css">
    <link rel="stylesheet" href="<?= $baseUrl ?>css/toast.css">
    <link rel="stylesheet" href="<?= $baseUrl ?>css/faq.css">
    <link rel="stylesheet" href="<?= $baseUrl ?>css/about.css">
    <link rel="stylesheet" href="<?= $baseUrl ?>css/tags.css">
    <!-- Add favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Crect x='25' y='25' width='50' height='50' fill='%233b82f6' transform='rotate(45 50 50)'/%3E%3Ctext x='50' y='58' font-family='Arial' font-size='24' font-weight='bold' fill='white' text-anchor='middle'%3ECT%3C/text%3E%3C/svg%3E">
    <!-- Preconnect to Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body>

    <header>
        <div class="header-container">
            <div class="logo">
                <!-- Use base URL for the logo link -->
                <a href="<?= $baseUrl ?>">CRYPTAG</a>
            </div>
            <nav>
                <ul>
                    <!-- Use base URL for the HOME link -->
                    <li><a href="<?= $baseUrl ?>">HOME</a></li>
                    <li><a href="<?= $baseUrl ?>about">ABOUT</a></li>
                    <li><a href="<?= $baseUrl ?>faq">FAQ</a></li>
                    <?php if (isset($currentUser) && $currentUser->getType() === 'registered' && $currentUser->isActive()): ?>
                        <li class="logged-in-only dropdown-container">
                            <div class="user-profile-icon" id="user-menu-button">
                                <i data-lucide="user"></i>
                            </div>
                            <div class="dropdown-menu" id="user-dropdown-menu">
                                <a href="<?= $baseUrl ?>notifications" class="dropdown-item">
                                    <p><i data-lucide="bell" class="dropdown-icon"></i> Notifications</p>
                                </a>
                                <a href="<?= $baseUrl ?>settings" class="dropdown-item">
                                    <p><i data-lucide="settings" class="dropdown-icon"></i> Settings</p>
                                </a>
                                <a href="#" class="dropdown-item logout-button">
                                    <p><i data-lucide="log-out" class="dropdown-icon"></i> Logout</p>
                                </a>
                            </div>
                        </li>
                    <?php else: ?>
                        <li class="logged-out-only"><a href="<?= $baseUrl ?>login">LOGIN</a></li>
                        <li class="logged-out-only"><a href="<?= $baseUrl ?>register">REGISTER</a></li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </header>

    <main class="container">
        <!-- The $content variable holds the rendered view -->
        <?= $content ?? '' ?>
    </main>

    <footer class="site-footer">
        <div class="footer-main-content">
            <div class="footer-column footer-column-left">
                <div class="footer-site-info">
                    <i data-lucide="shield-check"></i>
                    <span>CRYPTAG</span>
                </div>
                <p class="site-description">Your portal for crypto insights and wallet interactions.</p>
                <div class="footer-socials-inline">
                    <a href="https://x.com/walletags" target="_blank" rel="noopener noreferrer" aria-label="CRYPTAG on X"><i data-lucide="twitter"></i></a>
                    <a href="https://t.me/cryptags" target="_blank" rel="noopener noreferrer" aria-label="CRYPTAG on Telegram"><i data-lucide="send"></i></a>
                </div>
                <div class="footer-copyright-inline">
                    <p>&copy; <?= date('Y') ?> CRYPTAG. All rights reserved.</p>
                </div>
            </div>
            <div class="footer-column">
                <h4>Quick Links</h4>
                <ul>
                    <li><a href="<?= $baseUrl ?>">Home</a></li>
                    <li><a href="<?= $baseUrl ?>about">About</a></li>
                    <li><a href="<?= $baseUrl ?>roadmap">Roadmap</a></li>
                    <li><a href="<?= $baseUrl ?>contact">Contact Us</a></li>
                    <li><a href="<?= $baseUrl ?>terms">TOS</a></li>
                    <li><a href="<?= $baseUrl ?>privacy">Privacy Policy</a></li>
                </ul>
            </div>
            <div class="footer-column">
                <h4>Resources</h4>
                <ul>
                    <li><a href="https://etherscan.io" target="_blank" rel="noopener noreferrer">Etherscan</a></li>
                    <li><a href="https://btcscan.org" target="_blank" rel="noopener noreferrer">Btcscan</a></li>
                    <li><a href="https://solscan.io" target="_blank" rel="noopener noreferrer">Solscan</a></li>
                    <li><a href="https://axiom.trade/@cryptag" target="_blank" rel="noopener noreferrer">Axiom Trade</a></li>
                    <li><a href="https://dexscreener.com" target="_blank" rel="noopener noreferrer">DexScreener</a></li>
                    <li><a href="https://jup.ag" target="_blank" rel="noopener noreferrer">Jup.ag</a></li>
                    <li><a href="https://meteora.ag" target="_blank" rel="noopener noreferrer">Meteora.ag</a></li>
                </ul>
            </div>
        </div>
    </footer>

<!-- Define base URL for JavaScript -->
    <script>
        const baseUrl = '<?= $baseUrl ?>'; // Output PHP base URL into JS const
    </script>
    <!-- Lucide Icons CDN (Production version) -->
    <script src="https://unpkg.com/lucide@latest"></script>
    <!-- Link to JS files (if needed later, use base URL) -->
    <script src="<?= $baseUrl ?>js/toast.js"></script>
    <script src="<?= $baseUrl ?>js/auth.js"></script> <!-- Added authentication script -->
    <script src="<?= $baseUrl ?>js/captcha.js"></script> <!-- Added CAPTCHA management script -->
    <script src="<?= $baseUrl ?>js/message-renderer.js"></script> <!-- Added message rendering module -->
    <script src="<?= $baseUrl ?>js/tag-manager.js"></script> <!-- Added tag management module -->
    <script src="<?= $baseUrl ?>js/search.js"></script>
    <script src="<?= $baseUrl ?>js/profile.js"></script>
    <script src="<?= $baseUrl ?>js/icons.js"></script> <!-- Added Lucide icons script -->
    <script src="<?= $baseUrl ?>js/faq.js"></script> <!-- Added FAQ accordion script -->
    <!-- <script src="<?= $baseUrl ?>js/script.js"></script> -->
</body>
</html>
