<?php

namespace App\Core;

/**
 * Basic PSR-4 Autoloader
 *
 * Maps the App\ namespace to the src/ directory.
 */
class Autoloader
{
    /**
     * Registers the autoloader with SPL.
     *
     * @return void
     */
    public static function register(): void
    {
        spl_autoload_register([__CLASS__, 'loadClass']);
    }

    /**
     * Loads the class file for a given class name.
     *
     * @param string $className The fully qualified class name.
     * @return void
     */
    public static function loadClass(string $className): void
    {
        // Define the base namespace and directory
        $prefix = 'App\\';
        $baseDir = __DIR__ . '/../'; // Go up one level from Core to src/

        // Does the class use the namespace prefix?
        $len = strlen($prefix);
        if (strncmp($prefix, $className, $len) !== 0) {
            // No, move to the next registered autoloader (if any)
            return;
        }

        // Get the relative class name
        $relativeClass = substr($className, $len);

        // Replace the namespace prefix with the base directory, replace namespace
        // separators with directory separators in the relative class name, append
        // with .php
        $file = $baseDir . str_replace('\\', '/', $relativeClass) . '.php';

        // If the file exists, require it
        if (file_exists($file)) {
            require $file;
        }
    }
}