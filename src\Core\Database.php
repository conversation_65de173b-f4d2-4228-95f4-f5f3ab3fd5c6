<?php

namespace App\Core;

use PDO;
use PDOException;
use Exception;

/**
 * Database Connection Manager using PDO.
 *
 * Provides a singleton instance of the PDO connection.
 */
class Database
{
    private static ?PDO $instance = null;
    private static array $config = [];

    /**
     * Private constructor to prevent direct instantiation.
     */
    private function __construct() {}

    /**
     * Prevent cloning of the instance.
     */
    private function __clone() {}

    /**
     * Prevent unserialization of the instance.
     * @throws Exception
     */
    public function __wakeup()
    {
        throw new Exception("Cannot unserialize a singleton.");
    }

    /**
     * Loads the database configuration.
     * Should be called once during application bootstrap.
     *
     * @param array $config Database configuration array
     * @return void
     */
    public static function loadConfig(array $config): void
    {
        if (empty(self::$config)) { // Load only once
            self::$config = $config;
        }
    }

    /**
     * Get the singleton PDO database connection instance.
     *
     * @return PDO The PDO instance
     * @throws PDOException If connection fails
     * @throws Exception If configuration is not loaded
     */
    public static function getInstance(): PDO
    {
        if (self::$instance === null) {
            if (empty(self::$config)) {
                throw new Exception("Database configuration not loaded. Call Database::loadConfig() first.");
            }

            $dsn = sprintf(
                'mysql:host=%s;port=%d;dbname=%s;charset=%s',
                self::$config['host'],
                self::$config['port'],
                self::$config['dbname'],
                self::$config['charset'] ?? 'utf8mb4'
            );

            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION, // Throw exceptions on errors
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,       // Fetch associative arrays by default
                PDO::ATTR_EMULATE_PREPARES   => false,                  // Use native prepared statements
            ];

            try {
                self::$instance = new PDO($dsn, self::$config['user'], self::$config['password'], $options);
            } catch (PDOException $e) {
                // In a real app, log this error securely instead of echoing
                // Consider throwing a more generic exception or handling based on environment
                throw new PDOException("Database Connection Error: " . $e->getMessage(), (int)$e->getCode());
            }
        }

        return self::$instance;
    }
}