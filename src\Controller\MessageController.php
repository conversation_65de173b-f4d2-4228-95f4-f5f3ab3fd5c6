<?php

namespace App\Controller;

use App\Core\Database;
use App\Helpers\IpHelper;
use App\Helpers\JwtHelper;
use App\Helpers\UuidHelper;
use App\Model\Message;
use App\Model\RateLimit;
use App\Model\User; // Use the unified User model
use App\Model\Wallet;
use App\Service\CaptchaService; // Added CaptchaService
use Exception;

/**
 * MessageController
 *
 * Handles message operations
 */
class MessageController extends BaseController
{
    private $captchaService;

    public function __construct()
    {
        $this->captchaService = new CaptchaService();
    }

    /**
     * Add a new message
     * Supports both new API format (/api/messages) and legacy format (/api/address/{type}/{address}/add-message)
     */
    public function addMessage($type = null, $address = null)
    {
        // DEBUG: Log the request details
        error_log("=== MessageController::addMessage DEBUG ===");
        error_log("Request method: " . $_SERVER['REQUEST_METHOD']);
        error_log("Type parameter: " . ($type ?? 'null'));
        error_log("Address parameter: " . ($address ?? 'null'));
        // Get and validate input first (can only read php://input once)
        $rawInput = file_get_contents('php://input');
        $data = json_decode($rawInput, true);
        if (!$data) {
            $data = $_POST;
        }

        error_log("Raw input: " . $rawInput);
        error_log("POST data: " . json_encode($_POST));
        error_log("Parsed data: " . json_encode($data));
        error_log("Headers: " . json_encode(getallheaders()));
        error_log("Cookies: " . json_encode($_COOKIE));

        // Only accept POST requests
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            error_log("ERROR: Method not allowed");
            $this->sendJsonResponse(['success' => false, 'error' => 'Method not allowed'], 405);
            return;
        }

        // Get client IP
        $clientIp = IpHelper::getClientIp();
        error_log("Client IP: " . $clientIp);

        // Check if IP is in denylist
        if (RateLimit::isIpInDenylist($clientIp)) {
            error_log("ERROR: IP in denylist");
            $this->sendJsonResponse(['success' => false, 'error' => 'Too many requests. Please try again later.'], 429);
            return;
        }

        // Handle both legacy and new API formats
        if ($type && $address) {
            // Legacy format: extract from URL parameters
            $targetWalletAddress = rawurldecode($address);
            $walletType = rawurldecode($type);
            $messageContent = $data['message_content'] ?? '';
            $cfTurnstileResponse = $data['cf_turnstile_response'] ?? '';

            // For legacy format, also check if wallet_id is provided for validation
            $postedWalletId = isset($data['wallet_id']) ? (int)$data['wallet_id'] : 0;
        } else {
            // New format: extract from POST data
            $targetWalletAddress = $data['target_wallet_address'] ?? '';
            $walletType = $data['wallet_type'] ?? '';
            $messageContent = $data['message_content'] ?? '';
            $cfTurnstileResponse = $data['cf_turnstile_response'] ?? '';
            $postedWalletId = 0; // Not used in new format
        }

        // Validate input
        if (empty($targetWalletAddress) || empty($walletType) || empty($messageContent)) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Wallet address, type, and message content are required'], 400);
            return;
        }

        // Initialize user variables
        $userId = null;
        $userType = null;
        $jwtValidAndActive = false;

        // Check for JWT auth cookie
        error_log("Checking JWT authentication...");
        if (isset($_COOKIE[JwtHelper::AUTH_COOKIE_NAME])) {
            $jwt = $_COOKIE[JwtHelper::AUTH_COOKIE_NAME];
            error_log("JWT cookie found: " . substr($jwt, 0, 20) . "...");
            $payload = JwtHelper::validateJwt($jwt);
            error_log("JWT payload: " . json_encode($payload));

            if ($payload) {
                $userId = $payload['sub'];
                $userType = $payload['type'];
                $jwtValidAndActive = $payload['active'] ?? false;
                error_log("JWT valid. User ID: $userId, Type: $userType, Active: " . ($jwtValidAndActive ? 'true' : 'false'));

                // Check if we need to update last_active_at
                $currentTime = time();
                $lastActivityRecorded = $payload['lar'] ?? 0;

                if (($currentTime - $lastActivityRecorded) >= 600) { // 10 minutes
                    error_log("Updating last activity...");
                    // Update last_active_at in database
                    User::updateLastActiveAt($userId, $userType);

                    // Re-issue JWT with updated lar
                    $user = User::getById($userId, $userType);
                    if ($user && $user->isActive()) {
                        $newJwt = ($userType === 'anonymous_verified')
                            ? JwtHelper::generateAnonymousUserJwt($user->getId(), $user->getGeneratedUsername(), $user->isActive())
                            : JwtHelper::generateRegisteredUserJwt($user->getId(), $user->getUsername(), $user->isActive());
                        $expiry = ($userType === 'anonymous_verified') ? JwtHelper::ANONYMOUS_USER_EXPIRY : JwtHelper::REGISTERED_USER_EXPIRY;
                        JwtHelper::setAuthCookie($newJwt, $expiry);
                        error_log("JWT refreshed");
                    }
                }
            } else {
                error_log("JWT validation failed");
            }
        } else {
            error_log("No JWT cookie found");
        }

        // Handle authentication based on user type
        if (!$jwtValidAndActive) {
            // No valid JWT - anonymous user, require CAPTCHA
            error_log("Anonymous user - requiring CAPTCHA");
            if (empty($cfTurnstileResponse)) {
                error_log("ERROR: No CAPTCHA response provided");
                $this->sendJsonResponse(['success' => false, 'error' => 'CAPTCHA verification is required'], 400);
                return;
            }

            // Check CAPTCHA validation rate limit
            if (RateLimit::isCaptchaValidationLimitExceeded($clientIp)) {
                error_log("ERROR: CAPTCHA validation rate limit exceeded");
                RateLimit::addIpToDenylist($clientIp);
                $this->sendJsonResponse(['success' => false, 'error' => 'Too many CAPTCHA validation attempts. Please try again later.'], 429);
                return;
            }

            // Record the CAPTCHA validation attempt
            error_log("Recording CAPTCHA validation attempt");
            RateLimit::recordAttempt($clientIp, RateLimit::TYPE_IP_CAPTCHA_VALIDATION);

            // Verify CAPTCHA using CaptchaService
            error_log("Verifying CAPTCHA token");
            if (!$this->captchaService->verifyTurnstileToken($cfTurnstileResponse, $clientIp)) {
                error_log("ERROR: CAPTCHA verification failed");
                $this->sendJsonResponse(['success' => false, 'error' => 'CAPTCHA verification failed'], 400);
                return;
            }
            error_log("CAPTCHA verification successful");

            // Create anonymous user
            error_log("Creating anonymous user");
            try {
                $user = User::createAnonymousUser($clientIp);
                $userId = $user->getId();
                $userType = 'anonymous_verified';
                error_log("Anonymous user created with ID: $userId");

                // Generate JWT and CSRF token for anonymous user
                $jwt = JwtHelper::generateAnonymousUserJwt(
                    $user->getId(),
                    $user->getGeneratedUsername(),
                    $user->isActive()
                );
                $csrfToken = JwtHelper::generateCsrfToken();

                // Set cookies
                JwtHelper::setAuthCookie($jwt, JwtHelper::ANONYMOUS_USER_EXPIRY);
                JwtHelper::setCsrfCookie($csrfToken, JwtHelper::ANONYMOUS_USER_EXPIRY);
                error_log("Anonymous user cookies set successfully");
            } catch (Exception $e) {
                error_log("ERROR creating anonymous user: " . $e->getMessage());
                $this->sendJsonResponse(['success' => false, 'error' => 'Failed to create user account'], 500);
                return;
            }
        } else {
            // Valid JWT - check if registered or anonymous user
            if ($userType === 'registered') {
                // Registered user - validate CSRF token (no CAPTCHA needed)
                error_log("Registered user - validating CSRF token");
                $csrfToken = $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
                $csrfCookie = $_COOKIE[JwtHelper::CSRF_COOKIE_NAME] ?? '';

                error_log("CSRF token from header: " . $csrfToken);
                error_log("CSRF token from cookie: " . $csrfCookie);

                if (empty($csrfToken) || empty($csrfCookie) || !JwtHelper::validateCsrfToken($csrfToken, $csrfCookie)) {
                    error_log("CSRF token validation failed");
                    $this->sendJsonResponse(['success' => false, 'error' => 'CSRF token validation failed'], 403);
                    return;
                }
                error_log("CSRF token validation passed");
            } else {
                // Anonymous user with existing JWT - still require CAPTCHA
                error_log("Anonymous user with existing JWT - requiring CAPTCHA");
                if (empty($cfTurnstileResponse)) {
                    error_log("ERROR: No CAPTCHA response provided");
                    $this->sendJsonResponse(['success' => false, 'error' => 'CAPTCHA verification is required'], 400);
                    return;
                }

                // Verify CAPTCHA
                if (!$this->captchaService->verifyTurnstileToken($cfTurnstileResponse, $clientIp)) {
                    error_log("ERROR: CAPTCHA verification failed");
                    $this->sendJsonResponse(['success' => false, 'error' => 'CAPTCHA verification failed'], 400);
                    return;
                }
                error_log("CAPTCHA verification successful for existing anonymous user");
            }

            // Re-check user's active status in database
            $user = User::getById($userId, $userType);
            if (!$user || !$user->isActive()) {
                $this->sendJsonResponse(['success' => false, 'error' => 'User account is deactivated'], 403);
                return;
            }
        }

        // Check message posting rate limit
        $isAnonymous = $userType === 'anonymous_verified';
        if (RateLimit::isMessagePostLimitExceeded($userId, $isAnonymous)) {
            $this->sendJsonResponse(['success' => false, 'error' => 'Message posting rate limit exceeded. Please try again later.'], 429);
            return;
        }

        try {
            // Find or create wallet
            error_log("Finding or creating wallet for type: $walletType, address: $targetWalletAddress");
            $walletModel = new Wallet();
            $wallet = $walletModel->findOrCreateByTypeAndAddress($walletType, $targetWalletAddress);

            if (!$wallet) {
                error_log("ERROR: Failed to find or create wallet");
                $this->sendJsonResponse(['success' => false, 'error' => 'Invalid wallet address or type'], 400);
                return;
            }
            error_log("Wallet found/created with ID: " . $wallet['id']);

            // For legacy format, validate that posted wallet_id matches the URL wallet
            if ($type && $address && $postedWalletId > 0) {
                if ($postedWalletId !== (int)$wallet['id']) {
                    $this->sendJsonResponse(['success' => false, 'error' => 'Wallet ID mismatch'], 400);
                    return;
                }
            }

            // Sanitize message content (same as legacy implementation)
            $trimmedContent = trim($messageContent);
            $sanitizedContent = strip_tags($trimmedContent);

            if (empty($sanitizedContent)) {
                $this->sendJsonResponse(['success' => false, 'error' => 'Message content cannot be empty or contain only HTML tags'], 400);
                return;
            }

            // Save message using the new signature
            $messageModel = new Message();
            $messageId = $messageModel->save(
                $wallet['id'],
                $sanitizedContent,
                $userId,
                $isAnonymous,
                $clientIp
            );

            if (!$messageId) {
                $this->sendJsonResponse(['success' => false, 'error' => 'Failed to save message'], 500);
                return;
            }

            // Record successful message post
            RateLimit::recordAttempt($userId, RateLimit::TYPE_USER_MESSAGE_POST);

            // Return success response
            $this->sendJsonResponse([
                'success' => true,
                'message_id' => $messageId
            ], 201);
        } catch (Exception $e) {
            error_log("Message processing Exception: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->sendJsonResponse(['success' => false, 'error' => 'Failed to process message'], 500);
        }
    }

    // Removed the private verifyCaptcha method as it's now in CaptchaService
}
