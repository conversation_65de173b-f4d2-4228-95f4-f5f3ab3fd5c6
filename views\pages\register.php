<?php
// Get the base URL from config
$config = require __DIR__ . '/../../config/config.php';
$baseUrl = $config['app']['url'] ?? 'http://localhost/CRYPTAG';
// Ensure trailing slash
if (substr($baseUrl, -1) !== '/') {
    $baseUrl .= '/';
}
?>
<div class="container">
    <h1 class="page-header">REGSITER</h1> <!-- Moved heading outside auth-card and added main-heading class -->

    <div class="card auth-card">
        <form id="register-form">
            <div class="form-group">
                <div class="input-with-icon">
                    <i data-lucide="user" class="input-icon"></i>
                    <input type="text" id="username" name="username" placeholder="Username *" value="<?= htmlspecialchars($anonymousUsername ?? '', ENT_QUOTES, 'UTF-8') ?>" required>
                </div>

                <?php if (!empty($anonymousUsername)): ?>
                    <p class="form-hint" style="color: var(--color-secondary); font-style: normal; margin-top: 5px;">
                        <i data-lucide="info" style="width: 14px; height: 14px; vertical-align: middle; margin-right: 4px;"></i>
                        Your anonymous username has been prefilled. You can change it or keep it.
                    </p>
                <?php endif; ?>
            </div>



            <div class="form-group">
                <div class="input-with-icon">
                    <i data-lucide="mail" class="input-icon"></i>
                    <input type="email" id="email" name="email" placeholder="Email *">
                </div>
            </div>

            <div class="form-group">
                <div class="input-with-icon">
                    <i data-lucide="lock" class="input-icon"></i>
                    <input type="password" id="password" name="password" placeholder="Password *">
                </div>
            </div>

            <div class="form-group">
                <div class="input-with-icon">
                    <i data-lucide="lock" class="input-icon"></i>
                    <input type="password" id="password_confirm" name="password_confirm" placeholder="Confirm Password *">
                </div>
            </div>

            <div class="captcha-section" style="display: none;">
                <div class="cf-turnstile" id="cf-turnstile-container">
                    <!-- Turnstile widget will be rendered here -->
                </div>
                <input type="hidden" id="cf-turnstile-response" name="cf-turnstile-response">
            </div>
            <p class="form-hint">Please complete the CAPTCHA to register</p>

            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-full" id="register-button">
                    Register
                </button>
            </div>
        </form>

        <div class="auth-footer">
            <p>Already have an account? <a href="<?= $this->getBaseUrl(); ?>login">Login</a></p>
            <p class="terms-blurb">By registering an account you agree to our <a href="<?= $baseUrl ?>terms">Terms and Conditions</a> and <a href="<?= $baseUrl ?>privacy">Privacy Policy</a>.</p>
        </div>
    </div>
</div>



<!-- Set global variables for JavaScript -->
<script>
    window.baseUrl = '<?= $baseUrl ?>';
</script>

<!-- External JavaScript files -->
<script src="<?= $baseUrl ?>js/register.js"></script>


