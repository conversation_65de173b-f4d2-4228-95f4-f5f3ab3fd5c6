/**
 * Lucide Icons Integration
 * This file initializes Lucide icons and provides helper functions for icon usage
 */

document.addEventListener('DOMContentLoaded', () => {
    // Initialize all icons with data-lucide attribute
    // For UMD version, we just need to call createIcons() without specifying icons
    lucide.createIcons();

    // Replace existing icon elements with Lucide icons
    replaceExistingIcons();
});

/**
 * Replaces existing icon elements with Lucide icons
 * This function targets specific elements that use text-based icons
 * and replaces them with Lucide icons
 */
function replaceExistingIcons() {
    // Replace toast notification icons
    replaceToastIcons();

    // Replace action button icons
    replaceActionButtonIcons();
}

/**
 * Replaces toast notification icons with Lucide icons
 * This is done by modifying the ToastManager class's displayToast method
 */
function replaceToastIcons() {
    // Only modify if ToastManager exists
    if (typeof ToastManager !== 'undefined' && ToastManager.prototype.displayToast) {
        // Store the original method
        const originalDisplayToast = ToastManager.prototype.displayToast;

        // Override the displayToast method
        ToastManager.prototype.displayToast = function(toastData) {
            // Call the original method first
            originalDisplayToast.call(this, toastData);

            // Find the toast that was just created
            const toast = document.getElementById(`toast-${toastData.id}`);
            if (!toast) return;

            // Find the icon span
            const iconSpan = toast.querySelector('.toast-icon');
            if (!iconSpan) return;

            // Clear the text content
            iconSpan.textContent = '';

            // Create the appropriate Lucide icon based on type
            let iconName = 'info';
            switch (toastData.type) {
                case 'success':
                    iconName = 'check-circle';
                    break;
                case 'error':
                    iconName = 'x-circle';
                    break;
                case 'warning':
                    iconName = 'alert-triangle';
                    break;
                case 'info':
                    iconName = 'info';
                    break;
            }

            // Create the icon element
            const iconElement = document.createElement('i');
            iconElement.setAttribute('data-lucide', iconName);
            iconSpan.appendChild(iconElement);

            // Initialize the icon with the UMD version
            lucide.createIcons({
                attrs: {
                    'stroke-width': 2,
                    'width': '1.2em',
                    'height': '1.2em'
                }
            });
        };
    }
}

/**
 * Replaces action button icons with Lucide icons
 * This targets elements with the class 'button-icon'
 */
function replaceActionButtonIcons() {
    // Map of button text content to Lucide icon names (from the Lucide icon list)
    const iconMap = {
        '📋': 'clipboard-copy', // Using clipboard-copy from the Lucide list
        '🔍': 'external-link'   // Using external-link from the Lucide list
    };

    // Find all button icon elements
    const buttonIcons = document.querySelectorAll('.button-icon');

    buttonIcons.forEach(iconElement => {
        const textContent = iconElement.textContent.trim();
        const iconName = iconMap[textContent];

        if (iconName) {
            // Clear the text content
            iconElement.textContent = '';

            // Create the icon element
            const iconElement2 = document.createElement('i');
            iconElement2.setAttribute('data-lucide', iconName);
            iconElement.appendChild(iconElement2);

            // Initialize the icon with the UMD version
            lucide.createIcons({
                attrs: {
                    'stroke-width': 2,
                    'width': '1em',
                    'height': '1em'
                }
            });
        }
    });
}
