/* Toast Notification System */
.toast-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 350px;
    width: 100%;
}

.toast {
    background-color: var(--color-surface);
    color: var(--color-text-primary);
    border-radius: var(--radius-md);
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    font-size: 0.95rem;
    line-height: 1.5;
    transform: translateX(120%);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    border-left: 4px solid var(--color-primary);
    max-width: 100%;
    box-sizing: border-box;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.hide {
    transform: translateX(120%);
    opacity: 0;
}

.toast-content {
    flex: 1;
    padding-right: 10px;
}

.toast-close {
    background: none;
    border: none;
    color: var(--color-text-tertiary);
    cursor: pointer;
    font-size: 1.2rem;
    padding: 0;
    margin: 0;
    line-height: 1;
    transition: color 0.2s ease;
}

.toast-close:hover {
    color: var(--color-text-primary);
}

/* Toast types */
.toast-success {
    border-left-color: var(--color-secondary);
}

.toast-success .toast-icon {
    color: var(--color-secondary);
}

.toast-error {
    border-left-color: var(--color-error);
}

.toast-error .toast-icon {
    color: var(--color-error);
}

.toast-warning {
    border-left-color: #f59e0b; /* Amber */
}

.toast-warning .toast-icon {
    color: #f59e0b;
}

.toast-info {
    border-left-color: var(--color-primary);
}

.toast-info .toast-icon {
    color: var(--color-primary);
}

/* Toast icon */
.toast-icon {
    margin-right: 10px;
    font-size: 1.2rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Lucide icon styling */
.toast-icon i {
    width: 1.2em;
    height: 1.2em;
    stroke-width: 2;
}

/* Toast with progress bar */
.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: rgba(255, 255, 255, 0.2);
    width: 100%;
    transform-origin: left;
}

/* Animation for progress bar */
@keyframes toast-progress {
    0% { transform: scaleX(1); }
    100% { transform: scaleX(0); }
}

.toast-progress.active {
    animation: toast-progress 3s linear forwards;
}

/* Toast layout */
.toast-layout {
    display: flex;
    align-items: flex-start;
}
