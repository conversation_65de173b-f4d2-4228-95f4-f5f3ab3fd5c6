<?php
// Get the base URL from config
$config = require __DIR__ . '/../../config/config.php';
$baseUrl = $config['app']['url'] ?? 'http://localhost/CRYPTAG';
// Ensure trailing slash
if (substr($baseUrl, -1) !== '/') {
    $baseUrl .= '/';
}
?>
<div class="container">
    <h1 class="page-header">LOGIN</h1> <!-- Moved heading outside auth-card and added page-header class -->

    <div class="card auth-card"> <!-- Added 'card' class -->
        <form id="login-form">
            <div class="form-group">
                <div class="input-with-icon">
                    <i data-lucide="mail" class="input-icon"></i>
                    <input type="text" id="email_or_username" name="email_or_username" placeholder="Email or Username">
                </div>
            </div>

            <div class="form-group">
                <div class="input-with-icon">
                    <i data-lucide="lock" class="input-icon"></i>
                    <input type="password" id="password" name="password" placeholder="Password">
                </div>
            </div>

            <!-- Anonymous Account Linking Option -->
            <?php if (!empty($anonymousUsername) && !empty($anonymousUserId)): ?>
                <div class="form-group anonymous-link-section">
                    <label class="checkbox-container">
                        <input type="checkbox" id="link_anonymous_account" name="link_anonymous_account" checked>
                        <span class="checkmark"></span>
                        <span class="checkbox-text">
                            Link <strong><?= htmlspecialchars($anonymousUsername, ENT_QUOTES, 'UTF-8') ?></strong> to this account?
                        </span>
                    </label>
                    <p class="form-hint" style="margin-top: 5px; font-size: 0.85rem;">
                        <i data-lucide="info" style="width: 14px; height: 14px; vertical-align: middle; margin-right: 4px;"></i>
                        This will preserve your anonymous activity and link it to your account.
                    </p>
                </div>

            <?php endif; ?>

            <div class="captcha-section" style="display: none;">
                <div class="cf-turnstile" id="cf-turnstile-container">
                    <!-- Turnstile widget will be rendered here -->
                </div>
                <input type="hidden" id="cf-turnstile-response" name="cf-turnstile-response">
            </div>

            <p class="form-hint">
                Please complete the CAPTCHA to login
            </p>

            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-full" id="login-button">
                    Login
                </button>
            </div>
        </form>

        <div class="auth-footer">
            <p>Don't have an account? <a href="<?= $baseUrl ?>register">Register</a></p>
        </div>
    </div>
</div>



<!-- Set global variables for JavaScript -->
<script>
    window.baseUrl = '<?= $baseUrl ?>';
</script>

<!-- External JavaScript files -->
<script src="<?= $baseUrl ?>js/login.js"></script>


