<?php

namespace App\Controller;

/**
 * AuthViewController
 *
 * Handles the views for authentication pages
 */
class AuthViewController extends BaseController
{
    /**
     * Show the login page
     */
    public function login()
    {
        // If user is already logged in, redirect to homepage
        if ($this->currentUser && $this->currentUser->getType() === 'registered' && $this->currentUser->isActive()) {
            $this->redirect('/');
        }

        // Get anonymous user data for linking option if available
        $anonymousUsername = '';
        $anonymousUserId = '';

        if ($this->currentUser && $this->currentUser->getType() === 'anonymous_verified') {
            $anonymousUsername = $this->currentUser->getGeneratedUsername();
            $anonymousUserId = $this->currentUser->getId();
        }

        $this->render('pages/login', [
            'title' => 'Login - CRYPTAG',
            'description' => 'Login to your CRYPTAG account',
            'anonymousUsername' => $anonymousUsername,
            'anonymousUserId' => $anonymousUserId
        ]);
    }

    /**
     * Show the registration page
     */
    public function register()
    {
        // If user is already logged in, redirect to homepage
        if ($this->currentUser && $this->currentUser->getType() === 'registered' && $this->currentUser->isActive()) {
            $this->redirect('/');
        }

        // Get anonymous user data for prefilling if available
        $anonymousUsername = '';
        $anonymousUserId = '';

        if ($this->currentUser && $this->currentUser->getType() === 'anonymous_verified') {
            $anonymousUsername = $this->currentUser->getGeneratedUsername();
            $anonymousUserId = $this->currentUser->getId();
        }

        $this->render('pages/register', [
            'title' => 'Register - CRYPTAG',
            'description' => 'Create a new CRYPTAG account',
            'anonymousUsername' => $anonymousUsername,
            'anonymousUserId' => $anonymousUserId
        ]);
    }
}
